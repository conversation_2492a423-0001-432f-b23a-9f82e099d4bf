import {
  AliasNode,
  type AliasedExpression,
  type Expression,
  IdentifierNode,
  Kysely,
  type OperationNode,
  PostgresDialect,
  type Transaction,
  sql,
} from 'kysely';
import type { KyselifyDatabase } from 'kysely-supabase';
import pg from 'pg';

import { SERVER_ENV } from '@/server/env';

import type { Database } from './supabase.types';

export type KyselyDatabase = KyselifyDatabase<Database>;

export type KyselyTransaction = Transaction<KyselyDatabase>;

export type KyselyInstance = Kysely<KyselyDatabase>;

export type KyselyEnums = Database['public']['Enums'];

// Create a database pool with one connection.
export const kysely = new Kysely<KyselyDatabase>({
  dialect: new PostgresDialect({
    pool: new pg.Pool({
      connectionString: SERVER_ENV.DATABASE_URL,
    }),
  }),
});

export class CanEdit<T> implements Expression<T> {
  #userId: T;

  constructor(userId: T) {
    this.#userId = userId;
  }

  // This is a mandatory getter. You must add it and always return `undefined`.
  // The return type must always be `T | undefined`.
  get expressionType(): T | undefined {
    return undefined;
  }

  toOperationNode(): OperationNode {
    const json = JSON.stringify(this.#userId);
    // Most of the time you can use the `sql` template tag to build the returned node.
    // The `sql` template tag takes care of passing the `json` string as a parameter, alongside the sql string, to the DB.
    return sql`CAST(${json} AS JSONB)`.toOperationNode();
  }

  as<A extends string>(alias: A): AliasedCanEdit<T, A> {
    return new AliasedCanEdit(this, alias);
  }
}

class AliasedCanEdit<T, A extends string> implements AliasedExpression<T, A> {
  #expression: Expression<T>;
  #alias: A;

  constructor(expression: Expression<T>, alias: A) {
    this.#expression = expression;
    this.#alias = alias;
  }

  get expression(): Expression<T> {
    return this.#expression;
  }

  get alias(): A {
    return this.#alias;
  }

  toOperationNode(): AliasNode {
    return AliasNode.create(this.#expression.toOperationNode(), IdentifierNode.create(this.#alias));
  }
}
