import type { APIContext, GetStaticPathsResult } from 'astro';
import { jsonArrayFrom, jsonObjectFrom } from 'kysely/helpers/postgres';

import { Resvg } from '@resvg/resvg-js';

import { applyComputedGameSteam } from '@/database/gameSteam';
import { kysely } from '@/database/kysely';
import { getSupabaseServerClient } from '@/database/supabase.server';
import { getGameImageStr } from '@/helpers/images/tier-image-utils';

export const prerender = false;

const query = kysely
  .selectFrom('gameSteam as gs')
  .select([
    'gameName',
    'slug',
    'steamId',
    'hasImageHeader',
    'hasImageHeroCapsule',
    'hasImageLibraryHero',
    'hasImageLibraryPoster',
    'hasImagePageBgBlur',
  ]);

type Props = {};

type Params = {
  gameSlug: string;
};

export async function getStaticPaths(): Promise<GetStaticPathsResult> {
  const gameSteams = await query.execute();
  return gameSteams.map(game => ({
    params: { gameSlug: game.slug },
  }));
}

export async function GET({ params, request, cookies, redirect }: APIContext<Props, Params>) {
  const filePath = `og-image/games/${params.gameSlug}.png`;

  const supabase = getSupabaseServerClient(request.headers.get('Cookie'), cookies);
  const {
    data: { publicUrl },
  } = supabase.storage.from('images').getPublicUrl(filePath);

  try {
    const responseBefore = await fetch(publicUrl, { method: 'HEAD' });
    if (responseBefore.status == 200) {
      return redirect(publicUrl);
    }
  } catch {
    // Proceed to create image
  }

  const result = await query
    .where('slug', '=', params.gameSlug)
    .select(eb =>
      jsonArrayFrom(
        eb
          .selectFrom('tierItem as ti')
          .whereRef('ti.gameSteamId', '=', 'gs.steamId')
          .selectAll()
          .select(eb =>
            jsonObjectFrom(
              eb
                .selectFrom('tierLane as tl')
                .whereRef('tl.slug', '=', 'ti.tierLaneSlug')
                .selectAll(),
            ).as('tierLane'),
          ),
      ).as('tierItems'),
    )
    .executeTakeFirstOrThrow();

  if (!result) {
    return new Response(null, {
      status: 404,
      statusText: 'Not found',
    });
  }

  const gameSteam = applyComputedGameSteam(result);
  const { gameName, imageFinalTall, tierItems } = gameSteam;
  const index = tierItems.findLastIndex(r => r.reviewTitle);
  const reviewTitle = tierItems[index]?.reviewTitle ?? '';
  const publishDateFormatted = tierItems[index]?.publishDateFormatted ?? '';
  const reviewedTags = tierItems.map(r => r.tierLane?.label);
  const allTags = reviewedTags.join(' | ');

  const svg = await getGameImageStr({
    title: `${gameName} ${reviewTitle}`,
    subtitle: allTags,
    date: publishDateFormatted,
    image: imageFinalTall,
  });
  const png = new Resvg(svg).render().asPng();

  supabase.storage.from('images').upload(filePath, png);

  return new Response(png, {
    headers: { 'Content-Type': 'image/png' },
  });
}
