{"headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src https: data: 'unsafe-inline' 'unsafe-eval'"}, {"key": "Permissions-Policy", "value": "accelerometer=(), ambient-light-sensor=(), autoplay=(), battery=(), camera=(), cross-origin-isolated=(), display-capture=(), document-domain=(), encrypted-media=(), execution-while-not-rendered=(), execution-while-out-of-viewport=(), fullscreen=(self), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), navigation-override=(), payment=(), picture-in-picture=(self), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(self), usb=(), web-share=(self), xr-spatial-tracking=(), clipboard-read=(self), clipboard-write=(self), gamepad=(), speaker-selection=(self), conversion-measurement=(self), focus-without-user-activation=(), hid=(self), idle-detection=(), interest-cohort=(), serial=(), sync-script=()"}, {"key": "Referrer-Policy", "value": "no-referrer-when-downgrade"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/(blog|og-image|tiers|spaces|tools)(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=1, stale-while-revalidate=59"}]}, {"source": "/blog/(.*)", "headers": [{"key": "CDN-Cache-Control", "value": "max-age=43200"}]}, {"source": "/og-image/(.*)", "headers": [{"key": "CDN-Cache-Control", "value": "max-age=43200"}]}, {"source": "/(.*).xml", "headers": [{"key": "CDN-Cache-Control", "value": "max-age=43200"}]}], "redirects": []}