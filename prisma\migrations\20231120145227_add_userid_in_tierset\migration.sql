/*
  Warnings:

  - Made the column `tierSetSlug` on table `tierLane` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "tierLane" DROP CONSTRAINT "tierLane_tierSetSlug_fkey";

-- AlterTable
ALTER TABLE "tierLane" ALTER COLUMN "tierSetSlug" SET NOT NULL;

-- AlterTable
ALTER TABLE "tierSet" ADD COLUMN     "userId" TEXT;

-- AddForeignKey
ALTER TABLE "tierLane" ADD CONSTRAINT "tierLane_tierSetSlug_fkey" FOREIGN KEY ("tierSetSlug") REFERENCES "tierSet"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;
