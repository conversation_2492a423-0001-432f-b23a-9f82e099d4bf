/*
 Warnings:
 
 - Made the column `label` on table `tierLane` required. This step will fail if there are existing NULL values in that column.
 - Made the column `icon` on table `tierLane` required. This step will fail if there are existing NULL values in that column.
 - Made the column `mainColor` on table `tierLane` required. This step will fail if there are existing NULL values in that column.
 - Made the column `textColor` on table `tierLane` required. This step will fail if there are existing NULL values in that column.
 - Made the column `description` on table `tierLane` required. This step will fail if there are existing NULL values in that column.
 - Made the column `description` on table `tierSet` required. This step will fail if there are existing NULL values in that column.
 - Made the column `icon` on table `tierSet` required. This step will fail if there are existing NULL values in that column.
 - Made the column `label` on table `tierSet` required. This step will fail if there are existing NULL values in that column.
 - Made the column `mainColor` on table `tierSet` required. This step will fail if there are existing NULL values in that column.
 - Made the column `textColor` on table `tierSet` required. This step will fail if there are existing NULL values in that column.
 
 */
-- Clear NULL
UPDATE "tierLane"
SET "label" = COALESCE("label", ''),
  "icon" = COALESCE("icon", ''),
  "mainColor" = COALESCE("mainColor", ''),
  "textColor" = COALESCE("textColor", ''),
  "description" = COALESCE("description", '')
WHERE "label" IS NULL
  OR "icon" IS NULL
  OR "mainColor" IS NULL
  OR "textColor" IS NULL
  OR "description" IS NULL;
-- Clear NULL
UPDATE "tierSet"
SET "label" = COALESCE("label", ''),
  "icon" = COALESCE("icon", ''),
  "mainColor" = COALESCE("mainColor", ''),
  "textColor" = COALESCE("textColor", ''),
  "description" = COALESCE("description", '')
WHERE "label" IS NULL
  OR "icon" IS NULL
  OR "mainColor" IS NULL
  OR "textColor" IS NULL
  OR "description" IS NULL;
-- AlterTable
ALTER TABLE "tierLane"
ALTER COLUMN "label"
SET NOT NULL,
  ALTER COLUMN "icon"
SET NOT NULL,
  ALTER COLUMN "mainColor"
SET NOT NULL,
  ALTER COLUMN "textColor"
SET NOT NULL,
  ALTER COLUMN "description"
SET NOT NULL;
-- AlterTable
ALTER TABLE "tierSet"
ALTER COLUMN "description"
SET NOT NULL,
  ALTER COLUMN "icon"
SET NOT NULL,
  ALTER COLUMN "label"
SET NOT NULL,
  ALTER COLUMN "mainColor"
SET NOT NULL,
  ALTER COLUMN "textColor"
SET NOT NULL;