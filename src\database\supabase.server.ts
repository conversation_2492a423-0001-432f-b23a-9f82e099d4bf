import type { AstroCookies } from 'astro';

import { type CookieOptions, createServerClient, parseCookieHeader } from '@supabase/ssr';

import { SERVER_ENV } from '@/server/env';

export function getSupabaseServerClient(cookieHeader: string | {} | null, cookies: AstroCookies) {
  const supabase = createServerClient(
    SERVER_ENV.PUBLIC_SUPABASE_URL,
    SERVER_ENV.PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          const cookieStr = typeof cookieHeader === 'string' ? cookieHeader : '';
          const parsed = parseCookieHeader(cookieStr);
          return parsed.map(({ name, value }) => ({
            name,
            value: value ?? '',
          }));
        },
        setAll(cookiesToSet: { name: string; value: string; options: CookieOptions }[]) {
          cookiesToSet.forEach(({ name, value, options }) => cookies.set(name, value, options));
        },
      },
    },
  );
  return supabase;
}

export async function getSupabaseServerUser(
  cookieHeader: string | {} | null,
  cookies: AstroCookies,
) {
  const supabase = getSupabaseServerClient(cookieHeader, cookies);
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    if (error) {
      throw error;
    }
    return user;
  } catch (error) {
    // Only log if it's not the common AuthSessionMissingError
    if (!(error instanceof Error && error.message.includes('Auth session missing'))) {
      console.error('getSupabaseServerUser error:', error);
    }
    return null;
  }
}
