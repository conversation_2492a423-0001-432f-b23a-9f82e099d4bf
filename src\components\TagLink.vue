<script setup lang="ts">
const props = defineProps({
  tierLane: {
    type: Object as () => Pick<
      Types.TierLane,
      'tierSetSlug' | 'mainColor' | 'textColor' | 'label' | 'slug' | 'icon'
    >,
    required: true,
  },
  header: {
    type: Boolean,
    default: false,
  },
  noClick: {
    type: Boolean,
    default: false,
  },
});

const tierSetSlug = computed(() => props.tierLane.tierSetSlug ?? '');
const label = computed(() => props.tierLane.label ?? '');
const tierLaneSlug = computed(() => props.tierLane.slug);
const mainColor = computed(() => props.tierLane.mainColor);
const textColor = computed(() => props.tierLane.textColor ?? '#fff');
const icon = computed(() => props.tierLane.icon ?? '');
</script>

<template>
  <div
    :class="[
      `inline rounded-md px-2 leading-6 font-bold`,
      `criteria-${tierSetSlug}`,
      `criteria-${tierSetSlug}-${tierLaneSlug}`,
      { 'py-0.5 text-sm hover:ring-1 hover:ring-slate-500': header },
      { 'text-xs opacity-50 hover:opacity-100': !header },
      { [`before:content-['#']`]: !icon },
    ]"
    :style="{
      color: textColor,
      backgroundImage: mainColor
        ? `linear-gradient(to right, ${mainColor} 60%, rgb(var(--theme-text-rgb) / 0.5))`
        : '',
      pointerEvents: noClick ? 'none' : 'unset',
    }"
    :data-pagefind-filter="`${label}`"
    :data-pagefind-sort="`${label}[data-${tierSetSlug}]`"
    v-bind="{
      [`data-${tierSetSlug}`]: tierLaneSlug,
    }"
  >
    <a
      :href="`/blog/tiers/${tierSetSlug}/${tierLaneSlug}`"
      :aria-label="`View more games with ${tierSetSlug} ${tierLaneSlug}`"
      :aria-disabled="noClick"
      class="whitespace-pre-wrap"
    >
      <span>{{ icon }}</span>
      <slot>{{ label }}</slot>
    </a>
  </div>
</template>
