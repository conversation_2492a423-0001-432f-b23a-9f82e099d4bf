import { kysely } from '@/database/kysely';

import { createRouter, publicProcedure } from '../trpc';

export const spaceRouter = createRouter({
  getMine: publicProcedure.query(async ({ input, ctx: { user } }) => {
    if (!user) {
      return null;
    }

    const space = await kysely
      .selectFrom('space as sp')
      .select('slug')
      .where(eb =>
        eb.or([
          eb(
            eb.selectFrom('player as p').where('p.userId', '=', user.id).select('p.id'),
            'in',
            eb
              .selectFrom('playersOnSpaces as pos')
              .whereRef('pos.spaceId', '=', 'sp.slug')
              .select('pos.spaceId'),
          ),
        ]),
      )
      .executeTakeFirst();

    if (space) {
      return space;
    }
  }),
});

export type SpaceRouter = typeof spaceRouter;
