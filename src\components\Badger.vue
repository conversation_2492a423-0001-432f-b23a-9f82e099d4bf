<script setup lang="ts">
import * as _ from 'lodash-es';
import Tag from 'primevue/tag';
import { useToast } from 'primevue/usetoast';

import { Icon } from '@iconify/vue';

import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';
import { pinia, useUserStore } from '@/stores/user';

import AddTierSetEditor from './AddTierSetEditor.vue';
import GameCard, { type UpdatedTierItem } from './GameCard.vue';
import GameCardMini from './GameCardMini.vue';
import GameFilters, { type GameFilterType } from './GameFilters.vue';
import Ripple from './inspira-ui/Ripple.vue';

const toast = useToast();
const userStore = useUserStore(pinia);
const { space } = storeToRefs(userStore);

const isLoadingList = ref(false);

const games = ref<Awaited<ReturnType<typeof trpc.gameSteam.list.query>>['data']>([]);
const tierSets = ref<Awaited<ReturnType<typeof trpc.tierSet.retrieveMany.query>>>([]);
const totalGameCount = ref(0);

const editingTierSets = ref<
  Record<
    string,
    Parameters<typeof trpc.tierSet.update.mutate>[0] & {
      editing: boolean;
      loading: boolean;
    }
  >
>({});

const displayingIndex = ref(0);
const data = computed(() => games.value[displayingIndex.value]);

const selectedTierSets = ref(['hypeness', 'suckz']);

function startAdd() {
  const newSlug = `new-${tierSets.value.length + 1}`;
  tierSets.value.unshift({
    slug: newSlug,
    label: newSlug,
    tierLanes: [],
    permissionType: 'PUBLIC',
    spaceId: space.value?.id!,
    canEdit: true,
  });
  startEdit(newSlug);
}

function startEdit(tierSetSlug: string) {
  const tierSet = tierSets.value.find(t => t.slug === tierSetSlug)!;
  editingTierSets.value[tierSetSlug] = {
    editing: true,
    loading: false,

    tierSetSlug: tierSet.slug,
    label: tierSet.label,
    slug: tierSet.slug,
    tierLanes: tierSet.tierLanes.map(tl => ({
      ...tl,
      tierLaneSlug: tl.slug,
    })),
  };
}

function onGameUpdated(newItem: UpdatedTierItem) {
  const i = games.value.findIndex(g => g.steamId === newItem.gameSteamId);
  if (i > -1) {
    const exist = games.value[i];
    games.value.splice(i, 1, exist);
  }
}

async function retrieveTierSets() {
  tierSets.value = await trpc.tierSet.retrieveMany.query({
    // slugs: selectedTierSets.value,
    spaces: [1],
    tierLanes: { includeCount: false },
    tierItems: { take: 0 },
  });
}

const latestFilters = ref<GameFilterType>();

async function fetch(changedFilters?: GameFilterType) {
  if (isLoadingList.value) {
    return;
  }
  try {
    isLoadingList.value = true;

    const skip = changedFilters ? 0 : games.value.length;

    const f = changedFilters || latestFilters.value!;

    if (f.search) {
      const dataList = await trpc.gameSteam.search.query({
        search: f.search,
      });
      games.value = dataList.data;
      return;
    }

    latestFilters.value = f;

    console.log('Fetching:', f);

    const [dataList] = await Promise.all([
      trpc.gameSteam.list.query({
        filters: f.filters,
        skip,
        take: 10,
      }),
    ]);

    if (changedFilters) {
      // reset page
      games.value = dataList.data;
      displayingIndex.value = 0;
    } else {
      // Just fetch more
      games.value.push(...dataList.data);
    }

    totalGameCount.value = dataList.total;
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't search",
      detail: 'Maybe the tier is too big :(',
      life: 20000,
    });
  }
  isLoadingList.value = false;
}

watch(displayingIndex, i => {
  if (i >= games.value.length - 3 && totalGameCount.value > games.value.length) {
    fetch();
  }
});

onMounted(() => {
  retrieveTierSets();
});
</script>
<template>
  <PrimeToast />
  <PrimeConfirmDialog>
    <template #icon>
      <Icon icon="lucide:trash" class="mr-2 inline"></Icon>
    </template>
  </PrimeConfirmDialog>
  <div class="flex flex-col gap-2">
    <!-- Filters -->
    <GameFilters :tierSets="tierSets" :disabled="isLoadingList" @search="fetch" />

    <!-- Tiersets -->
    <PrimeMultiSelect
      v-model="selectedTierSets"
      :options="tierSets.map(ts => ({ label: ts.label, value: ts.slug, canEdit: ts.canEdit }))"
      :virtualScrollerOptions="{ itemSize: 44 }"
      optionLabel="label"
      optionValue="value"
      placeholder="Select tiersets..."
      display="chip"
      class="w-full bg-theme-bg text-xs"
      showClear
      filter
    >
      <template
        #option="{ option }: { option: { label: string; value: string; canEdit: boolean } }"
      >
        <div
          v-if="!editingTierSets[option.value]?.editing"
          class="align-items-center flex w-full justify-between text-sm"
        >
          <div class="flex gap-1">
            <div>{{ option.label }}</div>
            <button
              v-if="option.canEdit"
              :class="cn('rounded-md bg-gray-700 px-1 text-base')"
              @click.stop="startEdit(option.value)"
            >
              <Icon icon="lucide:edit" class="text-xs" />
            </button>
          </div>
        </div>
      </template>
      <template #footer>
        <button
          :class="cn('m-2 rounded-md bg-slate-600 px-2 py-1 text-base')"
          @click.stop="startAdd"
        >
          <div>+ New</div>
        </button>
      </template>
    </PrimeMultiSelect>

    <!-- Editing TierSet -->
    <template v-for="(ets, oldSlug) in editingTierSets">
      <AddTierSetEditor
        v-if="ets.editing"
        :key="oldSlug"
        v-model:tierSet="editingTierSets[oldSlug]"
        :old-slug="oldSlug"
        @finished="
          updated => {
            delete editingTierSets[oldSlug];
            tierSets = updated
              ? tierSets.map(ts => (ts.slug === oldSlug ? updated : ts))
              : tierSets;
          }
        "
        @deleted="
          () => {
            delete editingTierSets[oldSlug];
            tierSets = tierSets.filter(ts => ts.slug !== oldSlug);
          }
        "
      />
    </template>

    <!-- Cards -->
    <div
      :class="
        cn(
          'relative flex flex-row overflow-visible',
          'flex-wrap gap-0',
          'md:h-[840px] md:flex-nowrap md:gap-8',
        )
      "
    >
      <Ripple
        :class="cn('-z-40 h-full [mask-image:radial-gradient(white,transparent)]')"
        :circle-class="cn('border-[hsl(var(--primary))] bg-[white]/10 rounded-md')"
        :base-circle-size="500"
        :circle-opacity-downgrade-ratio="0"
        :wave-speed="100"
      />

      <!-- Main Card -->
      <div
        :class="cn('relative flex w-full flex-col gap-8 overflow-visible md:w-6/12', 'md:order-1')"
      >
        <GameCard
          v-if="data"
          :game="data"
          :tierSets="tierSets.filter(ts => selectedTierSets.includes(ts.slug))"
          :class="cn('m-auto max-w-prose')"
          :key="data.steamId"
          @updated="onGameUpdated"
        />
      </div>

      <!-- L-card -->
      <div :class="cn('overflow-visible', 'm-auto w-5/12', 'md:order-first md:m-0 md:w-3/12')">
        <div v-if="displayingIndex > 0" :class="cn('relative h-full w-full rounded-lg')">
          <button class="h-full p-1" @click="displayingIndex -= 1">
            <img
              :class="
                cn(
                  'h-full w-full rounded-lg object-cover',
                  'transition-all hover:opacity-100 md:opacity-5',
                  '[mask-image:linear-gradient(to_right,white_50%,transparent_100%)]',
                  'hover:[mask-image:linear-gradient(to_right,white_75%,transparent_100%)]',
                )
              "
              :src="games[displayingIndex - 1].imagePageBgBlur"
              :alt="games[displayingIndex - 1].gameName"
            />
          </button>
        </div>
      </div>

      <!-- R-card -->
      <div :class="cn('overflow-visible', 'm-auto w-5/12', 'md:order-last md:m-0 md:w-3/12')">
        <div
          v-if="displayingIndex < games.length - 1"
          :class="cn('relative h-full w-full rounded-lg')"
        >
          <button class="h-full p-1" @click="displayingIndex += 1">
            <img
              :class="
                cn(
                  'h-full w-full rounded-lg object-cover',
                  'transition-all hover:opacity-100 md:opacity-5',
                  '[mask-image:linear-gradient(to_left,white_50%,transparent_100%)]',
                  'hover:[mask-image:linear-gradient(to_left,white_75%,transparent_100%)]',
                )
              "
              :src="games[displayingIndex + 1].imagePageBgBlur"
              :alt="games[displayingIndex + 1].gameName"
            />
          </button>
        </div>
      </div>
    </div>

    <!-- Card List -->
    <div :class="cn('relative grid grid-cols-2 gap-1 md:grid-cols-10')" :key="`${displayingIndex}`">
      <GameCardMini
        v-for="(game, i) in games
          .slice(
            displayingIndex - _.clamp(displayingIndex, 5),
            displayingIndex - _.clamp(displayingIndex, 5) + 10,
          )
          .slice(0, 10)"
        :key="`${game.steamId}`"
        :game="game"
        :tierSets="tierSets.filter(ts => selectedTierSets.includes(ts.slug))"
        :selected="data.steamId === game.steamId"
        :class="cn('h-24')"
        @click="displayingIndex = games.findIndex(g => g.steamId === game.steamId)"
      />
      <Tag
        severity="secondary"
        :value="`${displayingIndex + 1}/${totalGameCount}`"
        class="absolute bottom-0 right-0 -mb-10"
      ></Tag>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
