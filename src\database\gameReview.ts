import { kysely } from './kysely';

export async function getStat() {
  return kysely.transaction().execute(async tx => {
    const promises = [
      tx
        .selectFrom('gameSteam')
        .select(eb => eb.fn.count<number>('id').as('count'))
        .executeTakeFirstOrThrow(),
      tx
        .selectFrom('tierReview')
        .where('reviewTitle', '!=', '')
        .select(eb => eb.fn.count<number>('gameSteamId').as('count'))
        .executeTakeFirstOrThrow(),
    ];
    const [{ count: all }, { count: reviewed }] = await Promise.all(promises);
    return {
      all,
      reviewed,
    };
  });
}
