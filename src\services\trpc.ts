import { createTRPCClient, httpLink } from '@trpc/client';
import { useCookies } from '@vueuse/integrations/useCookies';

import { transformer } from '@/helpers/transformer';
import type { AppRouter } from '@/server/routers';

const cookies = useCookies(['session']);

const getBaseUrl = () => {
  if (typeof window !== 'undefined') return ''; // browser should use relative url
  if (import.meta.env.VERCEL_URL) return `https://${import.meta.env.VERCEL_URL}`; // SSR should use vercel url
  return `http://localhost:${import.meta.env.PORT ?? 4321}`; // dev SSR should use localhost
};

export const trpc = createTRPCClient<AppRouter>({
  links: [
    httpLink({
      url: `${getBaseUrl()}/api/trpc`,
      transformer,
      headers() {
        return {
          'X-Session': cookies.get('session'),
        };
      },
    }),
  ],
});
