<script setup lang="ts">
const props = defineProps({
  url: {
    type: String,
    required: false,
  },
  bg: {
    type: String,
    required: false,
  },
  disabled: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const emit = defineEmits(['click']);
</script>

<template>
  <div
    :class="[
      'group relative flex h-[20rem] w-full max-w-[28rem] items-center justify-center',
      'overflow-hidden rounded-xl bg-clip-border text-center text-gray-700 shadow-lg',
      'transition-all',
    ]"
    :disabled="disabled"
    @click="emit('click')"
  >
    <img
      v-if="bg"
      :class="[
        'absolute inset-0 -left-2/4 m-0 h-full max-w-none overflow-hidden rounded-none bg-transparent',
        'bg-cover bg-clip-border bg-center text-gray-700 shadow-none',
        'transition-all group-hover:-left-3/4 group-hover:h-[120%] group-hover:shadow-2xl',
        'group-hover:animate-shake group-hover:animate-infinite',
        'group-hover:animate-duration-2000',
      ]"
      :src="bg"
      alt="background image"
    />
    <div
      :class="[
        'absolute inset-0 h-full w-full',
        'to-bg-black-10 bg-linear-to-t from-black/80 via-black/50',
        'transition-all duration-200 ease-out',
        'group-hover:from-orange-900/50 group-hover:via-orange-500/20',
      ]"
    ></div>
    <div class="relative transition-all group-hover:animate-jump">
      <a
        :class="[
          'mb-6 block font-sans text-4xl font-medium leading-[1.5] tracking-normal',
          'text-white antialiased',
          'transition-all duration-200 ease-out group-hover:mb-0 group-hover:text-white',
        ]"
        :href="url"
      >
        <slot name="title"></slot>
      </a>
      <a
        :class="[
          'mb-4 block font-sans text-xl font-semibold leading-snug tracking-normal',
          'text-gray-400 antialiased',
        ]"
        :href="url"
      >
        <slot name="body"></slot>
      </a>
      <div class="mt-2 hidden justify-center text-sm text-white group-hover:flex">
        <slot name="action"></slot>
      </div>
    </div>
  </div>
</template>
