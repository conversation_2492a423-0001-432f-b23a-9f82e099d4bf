---
import * as _ from 'lodash-es';
import type { BlogGameResult } from '@/server/routers/blog';

interface Props {
  game: BlogGameResult;
}

const { game } = Astro.props;

const gameName = game?.gameName ?? '';
const tierItems = game.tierItems;

const tierItemsToShow = [
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'event')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'hypeness')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'suckz')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'steam-tag')!,
].filter(ti => !!ti);

const reviewed = (tierItems.find(r => r.reviewTitle) as (typeof tierItems)[number]) ?? tierItems[0];

const reviewTitle = reviewed?.reviewTitle ?? '';
const targetUrl = (reviewed ? `/blog/${game?.slug}` : game?.gameUrl) ?? '#';

const tagText = tierItemsToShow
  .map(review => review.tierLane?.label)
  .filter(Boolean)
  .join(' · ');
---

<a
  href={targetUrl}
  rel="prefetch"
  class="block h-full w-full rounded-lg border border-gray-200/80 bg-white/80 p-4 shadow-sm transition-all duration-200 ease-in-out hover:border-gray-300 hover:bg-white hover:shadow-md dark:border-gray-700/50 dark:bg-gray-800/50 dark:text-gray-200 dark:hover:border-gray-600 dark:hover:bg-gray-800/80"
>
  <div class="flex flex-col gap-2">
    <!-- Main Title: Game Name + Review Title -->
    <h3 class="font-bold text-gray-900 dark:text-white">
      <span>{gameName}</span>
      <span class="ml-1 font-normal text-gray-600 dark:text-gray-300">{reviewTitle}</span>
    </h3>

    <!-- Tags rendered as a single line of plain text -->
    {tagText && <div class="text-xs font-medium text-[var(--theme-secondary)]">{tagText}</div>}
  </div>
</a>
