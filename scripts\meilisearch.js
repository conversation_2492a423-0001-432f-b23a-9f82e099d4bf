import { <PERSON><PERSON><PERSON>earch } from 'meilisearch';

import { PrismaClient } from '@prisma/client';

if (!process.env.PUBLIC_MEILISEARCH_HOST) {
  throw new Error('PUBLIC_MEILISEARCH_HOST is not set');
}
if (!process.env.MEILISEARCH_MASTER_KEY) {
  throw new Error('MEILISEARCH_MASTER_KEY is not set');
}

const client = new MeiliSearch({
  host: process.env.PUBLIC_MEILISEARCH_HOST,
  apiKey: process.env.MEILISEARCH_MASTER_KEY,
});

const prisma = new PrismaClient();

const totalGameSteams = await prisma.gameSteam.count();

const PER_PAGE = 1_000;

for (let offset = 0; offset < totalGameSteams; offset += PER_PAGE) {
  const gameSteams = await prisma.gameSteam.findMany({
    include: {
      tierItems: true,
    },
    take: PER_PAGE,
    skip: offset,
  });

  const data = gameSteams.map(gs => {
    gs.reviewTitle = '';
    gs.reviewContent = '';
    gs.tierItems.forEach(ti => {
      gs[ti.tierSetSlug] = ti.tierLaneSlug;
      if (ti.tierSetSlug === 'suckz') {
        if (ti.reviewTitle) {
          gs.reviewTitle = ti.reviewTitle;
        }
        if (ti.reviewContent) {
          gs.reviewContent = ti.reviewContent;
        }
        if (ti.publishDate) {
          gs.publishDate = ti.publishDate;
        }
      }
    });
    delete gs.tierItems;
    return gs;
  });

  console.log(`Adding ${data.length} games to MeiliSearch...`, data[0]);

  client
    .index('games')
    .addDocuments(JSON.parse(JSON.stringify(data)))
    .then(res => console.log(res));
}
