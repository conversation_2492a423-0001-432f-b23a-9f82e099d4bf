-- DropFore<PERSON><PERSON>ey
ALTER TABLE "playersOnSpaces" DROP CONSTRAINT "playersOnSpaces_playerId_fkey";

-- DropForeignKey
ALTER TABLE "playersOnSpaces" DROP CONSTRAINT "playersOnSpaces_spaceId_fkey";

-- DropForeignKey
ALTER TABLE "tierItem" DROP CONSTRAINT "tierItem_clonedFromTierItemId_fkey";

-- DropForeignKey
ALTER TABLE "tierItem" DROP CONSTRAINT "tierItem_fromLaneTierSetSlug_fromLaneSlug_fkey";

-- DropForeignKey
ALTER TABLE "tierItem" DROP CONSTRAINT "tierItem_gameSteamId_fkey";

-- DropForeignKey
ALTER TABLE "tierItem" DROP CONSTRAINT "tierItem_tierSetSlug_fkey";

-- DropForeignKey
ALTER TABLE "tierItem" DROP CONSTRAINT "tierItem_tierSetSlug_tierLaneSlug_fkey";

-- DropFore<PERSON><PERSON>ey
ALTER TABLE "tierSet" DROP CONSTRAINT "tierSet_spaceId_fkey";

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_gameSteamId_fkey" FOREIGN KEY ("gameSteamId") REFERENCES "gameSteam"("steamId") ON DELETE SET DEFAULT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_fromLaneTierSetSlug_fromLaneSlug_fkey" FOREIGN KEY ("fromLaneTierSetSlug", "fromLaneSlug") REFERENCES "tierLane"("tierSetSlug", "slug") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_tierSetSlug_fkey" FOREIGN KEY ("tierSetSlug") REFERENCES "tierSet"("slug") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_tierSetSlug_tierLaneSlug_fkey" FOREIGN KEY ("tierSetSlug", "tierLaneSlug") REFERENCES "tierLane"("tierSetSlug", "slug") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_clonedFromTierItemId_fkey" FOREIGN KEY ("clonedFromTierItemId") REFERENCES "tierItem"("id") ON DELETE SET DEFAULT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierSet" ADD CONSTRAINT "tierSet_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE SET DEFAULT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "playersOnSpaces" ADD CONSTRAINT "playersOnSpaces_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "playersOnSpaces" ADD CONSTRAINT "playersOnSpaces_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE CASCADE ON UPDATE CASCADE;
