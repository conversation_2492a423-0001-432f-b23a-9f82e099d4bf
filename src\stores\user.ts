import { defineStore } from 'pinia';

import type { User } from '@supabase/supabase-js';
import { useSessionStorage } from '@vueuse/core';

import { trpc } from '@/services/trpc';

export { pinia } from './';

type APIResponseMe = Exclude<Awaited<ReturnType<typeof trpc.user.getMine.query>>, null>;

const sessionMe = useSessionStorage('demoman-me', '');

export const useUserStore = defineStore('user', () => {
  const user = ref<User>();
  const player = ref<APIResponseMe['player']>();
  const space = ref<APIResponseMe['space']>();

  const avatarUrl = computed(() => {
    if (!user.value) {
      return '';
    }
    return user.value.user_metadata['avatar_url'];
  });

  const name = computed(() => {
    if (player.value) {
      return player.value.displayName;
    }
    if (user.value) {
      return user.value.user_metadata['name'];
    }
    return '';
  });

  const isDemomanSpace = computed(() => {
    return space.value?.slug === 'yay';
  });

  function setUser(val: User) {
    user.value = val;
  }

  function setPlayer(val: APIResponseMe['player']) {
    player.value = val;
  }

  function setSpace(val: APIResponseMe['space']) {
    space.value = val;
  }

  async function fetchUserInfo() {
    if (!user.value) {
      let me: APIResponseMe | null = null;
      if (sessionMe.value) {
        try {
          me = JSON.parse(sessionMe.value);
        } catch {
          // pass to next
        }
      }

      if (!me) {
        me = await trpc.user.getMine.query();
      }

      if (me) {
        if (me.user) {
          setUser(me.user);
        }
        if (me.player) {
          setPlayer(me.player);
        }
        if (me.space) {
          setSpace(me.space);
        }
        sessionMe.value = JSON.stringify(me);
      }
    }
    return user.value;
  }

  return {
    user,
    avatarUrl,
    name,
    player,
    space,
    isDemomanSpace,
    setUser,
    setPlayer,
    setSpace,
    fetchUserInfo,
  };
});
