/**
 * ensures the app isn't built with invalid env vars.
 */
import { z } from 'zod';

const envSchema = z.object({
  ASSETS_PREFIX: z.string().optional(),
  SITE: z.string().optional(),

  BASE_URL: z.string(),
  MODE: z.enum(['development', 'test', 'production']),
  DEV: z.boolean(),
  PROD: z.boolean(),
  SSR: z.boolean(),

  VERCEL_URL: z.string().optional(),
  PORT: z.number().int().optional().default(4321),

  DATABASE_URL: z.string().url(),
  DIRECT_URL: z.string(),

  SUPABASE_PROJECT_ID: z.string(),

  CF_ACCESS_CLIENT_ID: z.string(),
  CF_ACCESS_CLIENT_SECRET: z.string(),
  WINDMILL_TOKEN: z.string(),

  PUBLIC_SUPABASE_URL: z.string().url(),
  PUBLIC_SUPABASE_ANON_KEY: z.string(),

  PUBLIC_MEILISEARCH_HOST: z.string().url(),
  MEILISEARCH_MASTER_KEY: z.string().optional(),
});

const env = envSchema.safeParse(import.meta.env);

if (!env.success) {
  throw new Error(
    '❌ Invalid environment variables: ' + JSON.stringify(env.error.format(), null, 4),
  );
}
export const SERVER_ENV = env.data;
