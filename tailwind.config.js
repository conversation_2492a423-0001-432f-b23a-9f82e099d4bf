import { fontFamily } from 'tailwindcss/defaultTheme';
import plugin from 'tailwindcss/plugin';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{astro,html,js,jsx,md,svelte,ts,tsx,vue}',
    './node_modules/primevue/**/*.{vue,js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      screens: {},
      fontSize: {
        '5xs': ['0.15rem'],
        '4xs': ['0.2rem'],
        '3xs': ['0.25rem'],
        xxxs: ['0.25rem'],
        xxs: ['0.5rem'],
        '1.5xs': ['0.6rem'],
      },
      colors: {
        /* For InspiraUI */
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      /* End For InspiraUI */
      textShadow: {
        sm: '0 1px 2px var(--tw-shadow-color)',
        DEFAULT: '0 2px 4px var(--tw-shadow-color)',
        lg: '0 8px 16px var(--tw-shadow-color)',
      },
      fontFamily: {
        // Add any custom fonts here
        sans: ['Noto Sans Thai Looped', ...fontFamily.sans],
        serif: [...fontFamily.serif],
      },
      transitionProperty: {
        height: 'height',
      },
      animation: {
        wiggle:
          'wiggle var(--tw-animate-duration, 1s) var(--tw-animate-easing, cubic-bezier(0, 0, 0.2, 1)) var(--tw-animate-delay, 0s) var(--tw-animate-iteration, infinite) var(--tw-animate-fill, none)',
        glow: 'glow var(--tw-animate-duration, 1s) var(--tw-animate-easing, cubic-bezier(0, 0, 0.2, 1)) var(--tw-animate-delay, 0s) var(--tw-animate-iteration, infinite) var(--tw-animate-fill, none)',
        'glow-sm':
          'glow-sm var(--tw-animate-duration, 1s) var(--tw-animate-easing, cubic-bezier(0, 0, 0.2, 1)) var(--tw-animate-delay, 0s) var(--tw-animate-iteration, infinite) var(--tw-animate-fill, none)',
      },
      keyframes: {
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
        glow: {
          '0%, 100%': {
            textShadow:
              '0 0 2px #fff, 0 0 3px #fff, 0 0 4px var(--tw-shadow-color), 0 0 5px var(--tw-shadow-color), 0 0 6px var(--tw-shadow-color), 0 0 7px var(--tw-shadow-color), 0 0 8px var(--tw-shadow-color)',
          },
          '50%': {
            textShadow:
              '0 0 4px #fff, 0 0 6px var(--tw-shadow-color), 0 0 8px var(--tw-shadow-color), 0 0 10px var(--tw-shadow-color), 0 0 12px var(--tw-shadow-color), 0 0 14px var(--tw-shadow-color), 0 0 16px var(--tw-shadow-color)',
          },
        },
        'glow-sm': {
          '0%, 100%': {
            textShadow:
              '0 0 0px #fff, 0 0 1px #fff, 0 0 2px var(--tw-shadow-color), 0 0 3px var(--tw-shadow-color), 0 0 4px var(--tw-shadow-color), 0 0 5px var(--tw-shadow-color), 0 0 6px var(--tw-shadow-color)',
          },
          '50%': {
            textShadow:
              '0 0 1px #fff, 0 0 2px var(--tw-shadow-color), 0 0 3px var(--tw-shadow-color), 0 0 4px var(--tw-shadow-color), 0 0 5px var(--tw-shadow-color), 0 0 6px var(--tw-shadow-color), 0 0 7px var(--tw-shadow-color)',
          },
        },
      },
      gridTemplateColumns: {
        16: 'repeat(16, minmax(0, 1fr))',
        24: 'repeat(24, minmax(0, 1fr))',
        32: 'repeat(32, minmax(0, 1fr))',
        36: 'repeat(36, minmax(0, 1fr))',
        42: 'repeat(42, minmax(0, 1fr))',
        48: 'repeat(48, minmax(0, 1fr))',
      },
      typography: theme => ({
        cactus: {
          css: {
            '--tw-prose-body': 'var(--theme-text)',
            '--tw-prose-headings': 'var(--theme-secondary)',
            '--tw-prose-links': 'var(--theme-text)',
            '--tw-prose-bold': 'var(--theme-text)',
            '--tw-prose-bullets': 'var(--theme-text)',
            '--tw-prose-quotes': 'var(--theme-quote)',
            '--tw-prose-code': 'var(--theme-text)',
            '--tw-prose-hr': '0.5px dashed #666',
            '--tw-prose-th-borders': '#666',
          },
        },
        DEFAULT: {
          css: {
            a: {
              '@apply cactus-link no-underline': '',
            },
            strong: {
              fontWeight: '700',
            },
            code: {
              border: '1px dotted #666',
              borderRadius: '2px',
            },
            blockquote: {
              borderLeftWidth: 'none',
            },
            hr: {
              borderTopStyle: 'dashed',
            },
            thead: {
              borderBottomWidth: 'none',
            },
            'thead th': {
              fontWeight: '700',
              borderBottom: '1px dashed #666',
            },
            'tbody tr': {
              borderBottomWidth: 'none',
            },
            tfoot: {
              borderTop: '1px dashed #666',
            },
          },
        },
        sm: {
          css: {
            code: {
              fontSize: theme('fontSize.sm')[0],
              fontWeight: '400',
            },
          },
        },
      }),
    },
  },
  plugins: [
    require('@inspira-ui/plugins').setupInspiraUI,
    plugin(function ({ addComponents }) {
      addComponents({
        '.cactus-link': {
          inlineSize: 'fit-content',
          paddingLeft: '0.3rem',
          backgroundImage:
            'linear-gradient(135deg, rgb(150 150 150 / 65%) 0%, rgb(200 200 200 / 10%) 65%);',
          '&:hover': {
            backgroundImage:
              'linear-gradient(135deg, rgb(200 200 200 / 65%) 70%, rgb(255 255 255 / 10%) 100%);',
            color: 'var(--theme-primary)',
          },
        },
        '.dark .cactus-link': {
          backgroundImage: 'linear-gradient(135deg, rgb(0 0 0 / 65%) 0%, rgb(50 50 50 / 10%) 65%);',
          '&:hover': {
            backgroundImage:
              'linear-gradient(135deg, rgb(0 0 0 / 65%) 70%, rgb(100 100 100 / 10%) 100%);',
          },
        },
        '.title': {
          '@apply text-2xl font-semibold text-theme-secondary': {},
        },
      });
    }),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          'text-shadow': value => ({
            textShadow: value,
          }),
        },
        { values: theme('textShadow') },
      );
    }),
  ],
};
