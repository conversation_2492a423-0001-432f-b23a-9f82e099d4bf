---
export interface Props {
  readonly id?: string;
  readonly className?: string;
}

const {} = Astro.props;
---

<div class="wrapper mb-2">
  <div id="autocomplete"></div>
  <div id="hits"></div>
</div>

<style>
  @reference '@/styles/global.css';
  :global(.aa-Form) {
    @apply rounded-md! border-none! bg-transparent!;
  }
  :global(.aa-Panel) {
    --aa-icon-size: 150px;
    mark {
      @apply bg-theme-primary! text-gray-800!;
    }

    .aa-ItemContent {
      @apply flex!;
      .aa-ItemIcon {
        @apply h-full! w-1/6!;
        object {
          height: auto;
          width: auto;
          max-height: calc(var(--aa-icon-size) + var(--aa-spacing-half) - 8px);
          max-width: calc(var(--aa-icon-size) + var(--aa-spacing-half) - 8px);
        }
      }

      .aa-GameTags {
        @apply flex flex-wrap gap-0.5 text-xs;
        .aa-Tag {
          @apply bg-theme-primary text-theme-text;
        }
      }
    }
  }
</style>

<script>
  import LogoFlat from '@/assets/logo-flat.svg';
  import { slugifyWithSettings } from '@/helpers/utils';

  import { autocomplete } from '@algolia/autocomplete-js';
  import '@algolia/autocomplete-theme-classic';
  import {
    getMeilisearchResults,
    meilisearchAutocompleteClient,
  } from '@meilisearch/autocomplete-client';

  type Entry = {
    id: number;
    gameName: string;
    shortDescription: string;
    steamId: number;
    releaseDate: string;
    aboutTheGame: string;
    publishers: string[];
    developers: string[];
    updatedAt: string;
    reviewTitle: string;
    reviewContent: string;

    'steam-tag'?: string[];
    event?: string[];
    hypeness?: string[];
    suckz?: string[];
  };

  const DISPLAYING_FIELDS = ['steam-tag', 'event', 'hypeness', 'suckz'] as const;

  const searchClient = meilisearchAutocompleteClient({
    url: import.meta.env.PUBLIC_MEILISEARCH_HOST,
    apiKey: import.meta.env.PUBLIC_MEILISEARCH_SEARCH_KEY,
  });

  // console.log(
  //   import.meta.env.PUBLIC_MEILISEARCH_HOST,
  //   import.meta.env.PUBLIC_MEILISEARCH_SEARCH_KEY,
  // );

  autocomplete<Entry>({
    container: '#autocomplete',
    placeholder: 'Search for games',
    getSources({ query, setContext }) {
      return [
        {
          sourceId: 'games',
          getItems() {
            return getMeilisearchResults<Entry>({
              searchClient,
              queries: [
                {
                  indexName: 'games',
                  query,
                  params: {
                    hitsPerPage: 5,
                  },
                },
              ],
              // transformResponse({ results, hits }) {
              //   // setContext({
              //   //   nbProducts: results[0].nbHits,
              //   // });

              //   // You can now use `state.context.nbProducts`
              //   // anywhere where you have access to `state`.

              //   return hits;
              // },
            });
          },
          templates: {
            item({ item, components, html }) {
              return html`
                <div class="aa-ItemWrapper">
                  <a
                    href="${item.reviewTitle
                      ? `/blog/${slugifyWithSettings(item.gameName)}/`
                      : `https://store.steampowered.com/app/${item.steamId}/`}"
                    target="_blank"
                    alt="${item.gameName}"
                  >
                    <div class="aa-ItemContent">
                      <div class="aa-ItemIcon aa-ItemIcon--alignTop">
                        <object
                          data="https://cdn.akamai.steamstatic.com/steam/apps/${item.steamId}/library_600x900.jpg"
                          aria-label="${item.gameName}"
                        >
                          <img src="${LogoFlat.src}" alt="${item.gameName}" />
                        </object>
                      </div>
                      <div class="aa-ItemContentBody">
                        <div class="aa-ItemContentTitle">
                          ${components.Highlight({
                            hit: item,
                            attribute: 'gameName',
                          })}
                          ${item.reviewTitle ? ': ' : ''}
                          ${item.reviewTitle
                            ? components.Highlight({ hit: item, attribute: 'reviewTitle' })
                            : ''}
                        </div>
                        <div class="aa-ItemContentDescription flex w-full flex-col gap-2">
                          <div class="aa-GameTags flex flex-wrap gap-0.5 text-xs text-orange-950">
                            ${DISPLAYING_FIELDS.flatMap(key =>
                              (item[key] || []).map(
                                (_, index) =>
                                  html`<div class="rounded-md bg-orange-400 p-0.5">
                                    ${components.Highlight({
                                      hit: item,
                                      attribute: [key, index as any],
                                    })}
                                  </div>`,
                              ),
                            )}
                          </div>
                          <div>
                            ${components.Snippet({
                              hit: item,
                              attribute: 'reviewContent',
                            })}
                          </div>
                          <div>
                            ${components.Snippet({
                              hit: item,
                              attribute: 'shortDescription',
                            })}
                          </div>
                          <hr class="text-gray-500" />
                        </div>
                      </div>
                    </div>
                  </a>
                </div>
              `;
            },
          },
        },
      ];
    },
  });
</script>

<style is:global>
  :root.dark {
    --pagefind-ui-primary: #eeeeee;
    --pagefind-ui-text: #eeeeee;
    --pagefind-ui-background: #152028;
    --pagefind-ui-border: #152028;
    --pagefind-ui-tag: #152028;
  }
</style>
