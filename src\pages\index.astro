---
import ReviewBlogPost from '@/components/ReviewBlogPost.astro';
import ReviewBlogPostSmall from '@/components/ReviewBlogPostSmall.astro';
import RecentActivityBox from '@/components/RecentActivityBox.vue';
import SocialList from '@/components/SocialList.astro';
import Layout from '@/layouts/Layout.astro';

import { getServerTrpcCaller } from '@/server/caller';

export const prerender = true;

const { caller } = await getServerTrpcCaller(Astro, { prerender });

const { reviewedGames, recentGames } = await caller.blog.fetchGames();

const TIER_1_COUNT = 1;
const TIER_2_COUNT = 31;

const games1 = reviewedGames.slice(0, TIER_1_COUNT);
const games2 = reviewedGames.slice(TIER_1_COUNT, TIER_2_COUNT);
const games3 = reviewedGames.slice(TIER_2_COUNT);
---

<Layout title="Home" pagefindIgnore>
  <section class="px-2">
    <h1 class="title">Welcome</h1>
    <p class="mt-4">
      เว็บนี้ผมเอาไว้รวบรวม Demo เกมที่ผมเคยเล่นมา เพื่อที่จะได้ดูเอง และให้คนอื่นได้ดูง่ายๆ<br />
      เวลาเล่น Demo ใหม่ จะได้ไม่ต้องรอทำคลิป เพราะทำคลิปมันใช้เวลาเหลือเกิ๊นน<br />
      ซึ่งรีวิวทั้งหมดมาจากความเห็นของผมเอง ที่มีความลำเอียง เรื่องมาก<br />
      เหมือนมนุษย์ทั่วไป อาจจะไม่ตรงกับความเห็นของมนุษย์คนไหนเลย หรือคนส่วนใหญ่<br />
      ดังนั้นดูเอาความบันเทิง แล้วลองเล่นเองนะครับ Have fun :)
    </p>
    <SocialList class="mt-4" />
  </section>
  <section aria-label="Recent Activity" class="mt-4">
    <h2 class="title mb-4 text-xl">Played Recently</h2>
    <RecentActivityBox items={recentGames} client:load />
  </section>
  <section aria-label="Blog game list" class="mt-4">
    <h2 class="title mb-4 text-xl">Reviews</h2>
    <div class="mb-2 flex gap-4 font-mono text-xs text-gray-600 dark:text-gray-400">
      <span>Total: {reviewedGames.length}</span>
    </div>
    <div class="grid grid-cols-4 gap-4">
      {
        games1.map(item => (
          <div class="col-span-4 aspect-[16/9] md:col-span-4">
            <ReviewBlogPost game={item} ensureImage />
          </div>
        ))
      }
      {
        games2.map(item => (
          <div class="col-span-4 aspect-[16/9] md:col-span-2">
            <ReviewBlogPost game={item} ensureImage />
          </div>
        ))
      }
      {
        games3.map(item => (
          <div class="col-span-4 md:col-span-1">
            <ReviewBlogPostSmall game={item} />
          </div>
        ))
      }
    </div>
  </section>
</Layout>
