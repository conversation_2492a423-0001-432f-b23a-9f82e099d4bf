---
import TagLink from '@/components/TagLink.astro';
import { findValidImageUrlByFetch } from '@/helpers/utils';
import type { BlogGameResult } from '@/server/routers/blog';
import { Image } from 'astro:assets';
import * as _ from 'lodash-es';

interface Props {
  game: BlogGameResult;
  ensureImage?: boolean;
}

const { game, ensureImage = false } = Astro.props;

const gameName = game?.gameName ?? '';
const tierItems = game.tierItems;

const tierItemsToShow = [
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'event')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'hypeness')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'suckz')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'steam-tag')!,
].filter(ti => !!ti);

const reviewed = tierItems.find(r => r.reviewTitle) as (typeof tierItems)[number];

const title = reviewed?.fullTitle ?? '';
const reviewTitle = reviewed?.reviewTitle ?? '';
const reviewContent = reviewed?.reviewContent ?? '';
const targetUrl = (reviewed ? `/blog/${game?.slug}` : game?.gameUrl) ?? '#';

const publishDate = (reviewed?.publishDate as Date | string) || new Date();
const publishDateString =
  typeof publishDate === 'string' ? publishDate.toString() : publishDate.toISOString();
const publishDateFormatted = reviewed?.publishDateFormatted || publishDateString;

const finalImageUrl = await findValidImageUrlByFetch(game, ensureImage);
---

<div
  class="group relative block h-full w-full overflow-hidden rounded-xl bg-gray-900 shadow-lg transition-all duration-300 ease-in-out hover:scale-105"
>
  <Image
    src={finalImageUrl}
    alt={title}
    width={400}
    height={300}
    class="absolute inset-0 h-full w-full object-cover transition-opacity duration-300 ease-in-out group-hover:opacity-75"
  />

  <div class="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"></div>

  <a
    href={targetUrl}
    rel="prefetch"
    class="absolute inset-0 z-10"
    aria-label={`View details for ${gameName} ${reviewTitle}`}
  >
  </a>

  <div class="relative flex h-full flex-col justify-end p-4 text-white">
    <!-- Title Section -->
    <h3
      class="text-lg leading-tight font-bold tracking-tight [text-shadow:0_2px_4px_rgba(0,0,0,0.7)]"
    >
      <span>{gameName}</span>
      <span class="font-normal">{reviewTitle}</span>
    </h3>

    <!-- Review Quote & Date Section -->
    {
      reviewContent && (
        <div class="mt-2 h-0 items-center text-gray-200/90 opacity-0 transition-all duration-300 [text-shadow:0_1px_3px_rgba(0,0,0,0.7)] group-hover:h-auto group-hover:opacity-100">
          <time datetime={publishDateString} class="text-xs whitespace-nowrap">
            {publishDateFormatted}
          </time>
          <q class="line-clamp-2 italic">{reviewContent}</q>
        </div>
      )
    }

    <!-- Tags Section -->
    <div class="tags z-20 mt-2 flex flex-wrap gap-0.5 leading-0">
      {
        tierItemsToShow.map(
          review =>
            review.tierLane && (
              <div>
                <TagLink tierSetSlug={review.tierLane.tierSetSlug} tierLane={review.tierLane} />
              </div>
            ),
        )
      }
    </div>
  </div>
</div>
