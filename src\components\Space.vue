<script setup lang="ts">
import type { Selectable } from 'kysely';

import { Icon } from '@iconify/vue';

import AddTierCard from '@/components/AddTierCard.vue';
import type { KyselyDatabase } from '@/database/kysely';

import TierSetCard from './TierSetCard.vue';

defineProps({
  space: {
    type: Object as () => {
      tierSets?: (Selectable<KyselyDatabase['tierSet']> & {
        _count: { tierLanes: number; tierItems: number };
      })[];
    },
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <div>
    <div class="grid w-full gap-2 md:grid-cols-2">
      <div v-for="tierSet in space.tierSets" :key="tierSet.slug" class="w-full">
        <TierSetCard :tierSet="tierSet" />
      </div>
      <div v-if="canEdit" class="w-full">
        <AddTierCard />
      </div>
    </div>

    <div
      v-if="!canEdit && space.tierSets?.length === 0"
      class="flex h-64 w-full flex-col items-center justify-center gap-5 text-3xl text-theme-text"
    >
      <Icon icon="lucide:book-dashed"></Icon>
      <div>This space is so lonely</div>
      <div class="text-xxs">And it's not your fault.</div>
    </div>
  </div>
</template>
