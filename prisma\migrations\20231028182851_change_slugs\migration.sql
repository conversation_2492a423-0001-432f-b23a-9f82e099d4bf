/*
  Warnings:

  - You are about to drop the column `gameEventSlug` on the `gameBoard` table. All the data in the column will be lost.
  - You are about to drop the column `gameBoardId` on the `gameReview` table. All the data in the column will be lost.
  - You are about to drop the column `gameDescription` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the `gameEvent` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[slug]` on the table `gameBoard` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[gameBoardGroupSlug,tierSetSlug]` on the table `gameBoard` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[gameSteamId,gameBoardSlug]` on the table `gameReview` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `slug` to the `gameBoard` table without a default value. This is not possible if the table is not empty.
  - Added the required column `gameBoardSlug` to the `gameReview` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "gameBoard" DROP CONSTRAINT "gameBoard_gameEventSlug_fkey";

-- DropForeignKey
ALTER TABLE "gameReview" DROP CONSTRAINT "gameReview_gameBoardId_fkey";

-- DropIndex
DROP INDEX "gameBoard_gameEventSlug_tierSetSlug_key";

-- DropIndex
DROP INDEX "gameReview_gameSteamId_gameBoardId_key";

-- DropIndex
DROP INDEX "tierLane_slug_key";

-- AlterTable
ALTER TABLE "gameBoard" DROP COLUMN "gameEventSlug",
ADD COLUMN     "gameBoardGroupSlug" TEXT,
ADD COLUMN     "slug" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "gameReview" DROP COLUMN "gameBoardId",
ADD COLUMN     "gameBoardSlug" TEXT NOT NULL,
ADD COLUMN     "reviewContent" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "reviewTitle" TEXT NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE "gameSteam" DROP COLUMN "gameDescription";

-- DropTable
DROP TABLE "gameEvent";

-- CreateTable
CREATE TABLE "gameBoardGroup" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "label" TEXT NOT NULL DEFAULT '',
    "icon" TEXT NOT NULL DEFAULT '',
    "mainColor" TEXT NOT NULL DEFAULT '#000',
    "textColor" TEXT NOT NULL DEFAULT '#fff',
    "description" TEXT NOT NULL DEFAULT '',
    "steamSlug" TEXT NOT NULL DEFAULT '',
    "url" TEXT NOT NULL DEFAULT '',
    "img" TEXT NOT NULL DEFAULT '',
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gameBoardGroup_pkey" PRIMARY KEY ("slug")
);

-- CreateIndex
CREATE UNIQUE INDEX "gameBoardGroup_id_key" ON "gameBoardGroup"("id");

-- CreateIndex
CREATE UNIQUE INDEX "gameBoardGroup_slug_key" ON "gameBoardGroup"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "gameBoard_slug_key" ON "gameBoard"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "gameBoard_gameBoardGroupSlug_tierSetSlug_key" ON "gameBoard"("gameBoardGroupSlug", "tierSetSlug");

-- CreateIndex
CREATE UNIQUE INDEX "gameReview_gameSteamId_gameBoardSlug_key" ON "gameReview"("gameSteamId", "gameBoardSlug");

-- AddForeignKey
ALTER TABLE "gameReview" ADD CONSTRAINT "gameReview_gameBoardSlug_fkey" FOREIGN KEY ("gameBoardSlug") REFERENCES "gameBoard"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gameBoard" ADD CONSTRAINT "gameBoard_gameBoardGroupSlug_fkey" FOREIGN KEY ("gameBoardGroupSlug") REFERENCES "gameBoardGroup"("slug") ON DELETE SET NULL ON UPDATE CASCADE;
