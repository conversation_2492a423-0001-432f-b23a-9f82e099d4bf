<script setup lang="ts">
import * as _ from 'lodash-es';

import { useStorage, useVModels } from '@vueuse/core';

import type { trpc } from '@/services/trpc';

const props = withDefaults(
  defineProps<{
    selectedTierLanes?: string[];
    tierLanes?: Awaited<ReturnType<typeof trpc.tierLane.retrieveMany.query>>;
    disabled?: boolean;
  }>(),
  {
    selectedTierLanes: () => [],
    tierLanes: () => [],
    disabled: false,
  },
);

const emit = defineEmits<(e: 'update:selectedTierLanes', filters: string[]) => void>();

const state = useStorage<{ selectedTierLanes: string[] }>(
  'demoman-editing',
  {
    selectedTierLanes: [],
  },
  localStorage,
  {
    mergeDefaults: true,
  },
);

const { selectedTierLanes, tierLanes } = useVModels(props, emit);

const filterChoices = computed(() => [
  {
    label: 'Tags',
    items: tierLanes.value.map(tl => ({
      value: tl.slug,
      label: `${tl.icon} ${tl.label}`,
      mainColor: tl.mainColor,
      textColor: tl.textColor,
    })),
  },
]);

watch(
  state,
  () => {
    selectedTierLanes.value = state.value.selectedTierLanes.length
      ? state.value.selectedTierLanes
      : selectedTierLanes.value;
  },
  { deep: true, immediate: true },
);

watch(
  selectedTierLanes,
  vals => {
    state.value.selectedTierLanes = vals;
  },
  { deep: true },
);
</script>

<template>
  <PrimeMultiSelect
    v-model="selectedTierLanes"
    :disabled="disabled"
    :virtualScrollerOptions="{ itemSize: 44 }"
    :options="filterChoices"
    optionGroupLabel="label"
    optionGroupChildren="items"
    optionLabel="label"
    optionValue="value"
    display="chip"
    class="w-full text-sm"
    showClear
    filter
    :style="{
      '--p-multiselect-background': 'var(--p-bg)',
    }"
  >
    <template #optiongroup="{ option }: { option: { label: string; value: string } }">
      <div>
        {{ option.label }}
      </div>
    </template>
    <template
      #option="{
        option,
      }: {
        option: { label: string; value: string; mainColor: string; textColor: string };
      }"
    >
      <div
        :style="{
          '--main': option.mainColor || 'var(--color-primary)',
          '--text': option.textColor,
        }"
      >
        <div class="animate-glow-sm font-black text-(--text) shadow-(--main)">
          {{ option.label }}
        </div>
      </div>
    </template>
  </PrimeMultiSelect>
</template>

<style scoped lang="scss"></style>
