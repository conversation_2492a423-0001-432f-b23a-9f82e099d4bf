{"extends": "astro/tsconfigs/strict", "compilerOptions": {"baseUrl": "src", "paths": {"@/*": ["./*"], "path": ["node:path"], "fs": ["node:fs"], "os": ["node:os"], "tty": ["node:tty"], "util": ["node:util"]}, "typeRoots": ["./types", "./src/types"], "jsx": "preserve", "types": ["unplugin-icons/types/astro"]}, "include": ["**/*.ts", "**/*.d.ts", "**/*.astro", "**/*.vue", "tailwind.config.js"], "exclude": ["node_modules", "functions/**/*"]}