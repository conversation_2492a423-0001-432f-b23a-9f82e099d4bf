-- CreateTable
CREATE TABLE "game_steam" (
    "id" SERIAL NOT NULL,
    "steam_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL DEFAULT '',
    "url" TEXT NOT NULL DEFAULT '',
    "img" TEXT NOT NULL DEFAULT '',
    "type" TEXT,
    "required_age" INTEGER,
    "is_free" BOOLEAN,
    "controller_support" TEXT,
    "dlc" JSONB,
    "detailed_description" TEXT,
    "about_the_game" TEXT,
    "short_description" TEXT,
    "supported_languages" TEXT,
    "reviews" TEXT,
    "header_image" TEXT,
    "website" TEXT,
    "pc_requirements" JSONB,
    "mac_requirements" JSONB,
    "linux_requirements" JSONB,
    "legal_notice" TEXT,
    "developers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "publishers" JSONB[] DEFAULT ARRAY[]::JSON<PERSON>[],
    "demos" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "package_groups" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "platforms" JSONB,
    "categories" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "genres" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "store_tags" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "screenshots" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "movies" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "release_date" JSONB,
    "support_info" JSONB,
    "background" TEXT,
    "price_overview" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "game_steam_pkey" PRIMARY KEY ("steam_id")
);

-- CreateTable
CREATE TABLE "game_review" (
    "id" SERIAL NOT NULL,
    "game_steam_id" INTEGER NOT NULL,
    "game_board_id" INTEGER NOT NULL,
    "tier_lane_slug" TEXT,
    "lexorank" TEXT,
    "note" TEXT NOT NULL DEFAULT '',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "game_review_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tier_lane" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "main_color" TEXT NOT NULL,
    "text_color" TEXT NOT NULL,
    "icon" TEXT NOT NULL,
    "score" INTEGER NOT NULL DEFAULT 0,
    "lexorank" TEXT,
    "tier_set_slug" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tier_lane_pkey" PRIMARY KEY ("slug")
);

-- CreateTable
CREATE TABLE "tier_set" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tier_set_pkey" PRIMARY KEY ("slug")
);

-- CreateTable
CREATE TABLE "game_event" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "url" TEXT NOT NULL DEFAULT '',
    "img" TEXT NOT NULL DEFAULT '',
    "started_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ended_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "game_event_pkey" PRIMARY KEY ("slug")
);

-- CreateTable
CREATE TABLE "game_board" (
    "id" SERIAL NOT NULL,
    "game_event_slug" TEXT NOT NULL,
    "tier_set_slug" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "game_board_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "game_steam_id_key" ON "game_steam"("id");

-- CreateIndex
CREATE UNIQUE INDEX "game_steam_steam_id_key" ON "game_steam"("steam_id");

-- CreateIndex
CREATE UNIQUE INDEX "game_review_id_key" ON "game_review"("id");

-- CreateIndex
CREATE UNIQUE INDEX "game_review_game_steam_id_game_board_id_key" ON "game_review"("game_steam_id", "game_board_id");

-- CreateIndex
CREATE UNIQUE INDEX "tier_lane_id_key" ON "tier_lane"("id");

-- CreateIndex
CREATE UNIQUE INDEX "tier_lane_slug_key" ON "tier_lane"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "tier_set_id_key" ON "tier_set"("id");

-- CreateIndex
CREATE UNIQUE INDEX "tier_set_slug_key" ON "tier_set"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "game_event_id_key" ON "game_event"("id");

-- CreateIndex
CREATE UNIQUE INDEX "game_event_slug_key" ON "game_event"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "game_board_id_key" ON "game_board"("id");

-- CreateIndex
CREATE UNIQUE INDEX "game_board_game_event_slug_tier_set_slug_key" ON "game_board"("game_event_slug", "tier_set_slug");

-- AddForeignKey
ALTER TABLE "game_review" ADD CONSTRAINT "game_review_game_steam_id_fkey" FOREIGN KEY ("game_steam_id") REFERENCES "game_steam"("steam_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "game_review" ADD CONSTRAINT "game_review_game_board_id_fkey" FOREIGN KEY ("game_board_id") REFERENCES "game_board"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "game_review" ADD CONSTRAINT "game_review_tier_lane_slug_fkey" FOREIGN KEY ("tier_lane_slug") REFERENCES "tier_lane"("slug") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tier_lane" ADD CONSTRAINT "tier_lane_tier_set_slug_fkey" FOREIGN KEY ("tier_set_slug") REFERENCES "tier_set"("slug") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "game_board" ADD CONSTRAINT "game_board_game_event_slug_fkey" FOREIGN KEY ("game_event_slug") REFERENCES "game_event"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "game_board" ADD CONSTRAINT "game_board_tier_set_slug_fkey" FOREIGN KEY ("tier_set_slug") REFERENCES "tier_set"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;
