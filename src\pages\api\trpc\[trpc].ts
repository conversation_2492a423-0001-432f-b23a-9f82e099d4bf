import type { APIRoute } from 'astro';

import { fetchRequestHandler } from '@trpc/server/adapters/fetch';

import { getCreateContextFunction } from '@/server/context';
import { SERVER_ENV } from '@/server/env';
import { appRouter } from '@/server/routers';

export const prerender = false;

export const ALL: APIRoute = ({ request, cookies }) => {
  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req: request,
    router: appRouter,
    createContext: getCreateContextFunction({ cookies }),
    onError: SERVER_ENV.DEV
      ? ({ path, error }) => {
          console.error(`❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`);
        }
      : undefined,
    // responseMeta(opts) {
    //   const { ctx, paths, errors, type } = opts;
    //   console.log(opts);
    //   // all cached routes
    //   const allPublic = paths?.every(path => path.startsWith('blog.'));
    //   // checking that no procedures errored
    //   const allOk = errors.length === 0;
    //   // checking we're doing a query request
    //   const isQuery = type === 'query';
    //   if (ctx?.resHeaders && allPublic && allOk && isQuery) {
    //     // cache request for 1 day + revalidate once every second
    //     const ONE_DAY_IN_SECONDS = 60 * 60 * 24;
    //     return {
    //       headers: {
    //         'cache-control': `s-maxage=1, stale-while-revalidate=${ONE_DAY_IN_SECONDS}`,
    //       },
    //     };
    //   }
    //   return {};
    // },
  });
};
