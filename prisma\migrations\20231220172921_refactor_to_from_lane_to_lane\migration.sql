/*
  Warnings:

  - You are about to drop the `tierSetItemSource` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "tierSetItemSource" DROP CONSTRAINT "tierSetItemSource_sourceTierLaneId_fkey";

-- DropForeignKey
ALTER TABLE "tierSetItemSource" DROP CONSTRAINT "tierSetItemSource_targetTierSetSlug_fkey";

-- DropTable
DROP TABLE "tierSetItemSource";

-- CreateTable
CREATE TABLE "fromLaneToLane" (
    "id" SERIAL NOT NULL,
    "fromLaneId" INTEGER NOT NULL,
    "toLaneId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "fromLaneToLane_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "fromLaneToLane_id_key" ON "fromLaneToLane"("id");

-- AddForeignKey
ALTER TABLE "fromLaneToLane" ADD CONSTRAINT "fromLaneToLane_fromLaneId_fkey" FOREIGN KEY ("fromLaneId") REFERENCES "tierLane"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "fromLaneToLane" ADD CONSTRAINT "fromLaneToLane_toLaneId_fkey" FOREIGN KEY ("toLaneId") REFERENCES "tierLane"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
