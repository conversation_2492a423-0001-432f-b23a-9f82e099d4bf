import { jsonArrayFrom, jsonObjectFrom } from 'kysely/helpers/postgres';
import * as _ from 'lodash-es';

import { applyComputedGameSteam } from '@/database/gameSteam';
import { kysely } from '@/database/kysely';
import { applyComputedTierSet } from '@/database/tierSet';
import { logQuery } from '@/helpers/utils';

import { createRouter, publicProcedure } from '../trpc';

const getLogPrefix = () => {
  return [new Date().toLocaleTimeString(), '[doFetchAll]'];
};

let fetchedGames = false;
let fetchingGames = false;

let fetchedTiers = false;
let fetchingTiers = false;

const fetchGames = async () => {
  // Reviewed games
  const gamesQuery = kysely
    .selectFrom('gameSteam as gs')
    .innerJoin('tierItem as ti', 'ti.gameSteamId', 'gs.steamId')
    .where('gs.slug', '!=', '')
    // .where('ti.publishDate', 'is not', null)
    // .where('ti.reviewTitle', '!=', '')
    // .orderBy('ti.publishDate', 'desc')
    .where('ti.tierLaneSlug', 'like', 'suckz%')
    .where('ti.tierLaneSlug', 'not like', 'suckz-z%')
    .distinctOn('gs.steamId')
    .select([
      'developers',
      'gameName',
      'gameUrl',
      'hasImageHeader',
      'hasImageHeroCapsule',
      'hasImageLibraryHero',
      'hasImageLibraryPoster',
      'hasImagePageBgBlur',
      'movies',
      'publishers',
      'releaseDate',
      'screenshots',
      'shortDescription',
      'slug',
      'steamId',
    ])
    .select(eb =>
      jsonObjectFrom(
        eb.selectFrom('tierReview as tr').whereRef('tr.gameSteamId', '=', 'gs.steamId').selectAll(),
      ).as('tierReview'),
    )
    .select(eb => [
      jsonArrayFrom(
        eb
          .selectFrom('tierItem as ti')
          .whereRef('ti.gameSteamId', '=', 'gs.steamId')
          .select([
            'ti.id',
            'ti.tierLaneSlug',
            'ti.reviewTitle',
            'ti.reviewContent',
            'ti.publishDate',
            'ti.createdAt',
          ])
          .select(ebti => [
            jsonObjectFrom(
              ebti
                .selectFrom('tierLane as tl')
                .whereRef('tl.slug', '=', 'ti.tierLaneSlug')
                .select([
                  'tl.label',
                  'tl.slug',
                  'tl.tierSetSlug',
                  'tl.mainColor',
                  'tl.textColor',
                  'tl.icon',
                ]),
            ).as('tierLane'),
          ]),
      ).as('tierItems'),
    ]);

  logQuery(gamesQuery);
  const res = await gamesQuery.execute();

  const games = res.map(applyComputedGameSteam);

  const reviewed = games
    .filter(item => item.tierReview?.reviewTitle && item.tierReview?.publishDate)
    .toSorted((a, b) => (a.tierReview?.publishDate! > b.tierReview?.publishDate! ? -1 : 1));

  const recent = games.toSorted((a, b) =>
    a.tierReview?.createdAt! > b.tierReview?.createdAt! ? -1 : 1,
  );

  console.log(...getLogPrefix(), 'fetched gamesQuery...');
  blogCache.reviewedGames = reviewed;
  blogCache.recentGames = recent;
  console.log(...getLogPrefix(), 'games:', blogCache.reviewedGames.length);
  console.log(...getLogPrefix(), 'recent:', blogCache.recentGames.length);
  return res;
};

const fetchTiers = async () => {
  const tierSetQuery = kysely
    .selectFrom('tierSet as ts')
    .where('ts.spaceId', '=', 1)
    .where('ts.permissionType', '=', 'PUBLIC')
    .selectAll()
    .select(eb => [
      jsonArrayFrom(
        eb
          .selectFrom('tierLane as tl')
          .whereRef('tl.tierSetSlug', '=', 'ts.slug')
          .selectAll()
          .orderBy('tl.score', 'desc')
          .orderBy('tl.createdAt', 'desc')
          .select(ebtl => [
            jsonArrayFrom(
              ebtl
                .selectFrom('tierItem as ti')
                .whereRef('ti.tierLaneSlug', '=', 'tl.slug')
                .orderBy('ti.id', 'desc')
                .limit(100)
                .select(['gameSteamId', 'publishDate', 'reviewContent', 'reviewTitle'])
                .select(ebti => [
                  jsonObjectFrom(
                    ebti
                      .selectFrom('gameSteam as gs')
                      .whereRef('gs.steamId', '=', 'ti.gameSteamId')
                      .select([
                        'gameName',
                        'gameUrl',
                        'hasImageHeader',
                        'hasImageHeroCapsule',
                        'hasImageLibraryHero',
                        'hasImageLibraryPoster',
                        'hasImagePageBgBlur',
                        'screenshots',
                        'slug',
                        'steamId',
                      ])

                      .select(ebgs => [
                        jsonArrayFrom(
                          ebgs
                            .selectFrom('tierItem as ti')
                            .whereRef('ti.gameSteamId', '=', 'gs.steamId')
                            .select(['tierLaneSlug', 'reviewTitle', 'publishDate']),
                        ).as('tierItems'),
                      ]),
                  ).as('gameSteam'),
                ]),
            ).as('tierItems'),
          ]),
      ).as('tierLanes'),
    ]);

  logQuery(tierSetQuery);
  const res = await tierSetQuery.execute();
  console.log(...getLogPrefix(), 'fetched tiers...');
  blogCache.tierSets.push(...res.map(applyComputedTierSet));
  console.log(...getLogPrefix(), 'tiersets:', blogCache.tierSets.length);
  return res;
};

export type BlogGameResult = ReturnType<
  typeof applyComputedGameSteam<Awaited<ReturnType<typeof fetchGames>>[number]>
>;
export type BlogTierSetResult = ReturnType<
  typeof applyComputedTierSet<Awaited<ReturnType<typeof fetchTiers>>[number]>
>;

export const blogCache = {
  reviewedGames: [] as BlogGameResult[],
  recentGames: [] as BlogGameResult[],
  tierSets: [] as BlogTierSetResult[],
};

export const blogRouter = createRouter({
  // ---------------------------
  fetchGames: publicProcedure.query(async () => {
    if (fetchedGames) {
      return { reviewedGames: blogCache.reviewedGames, recentGames: blogCache.recentGames };
    }
    if (fetchingGames) {
      return { reviewedGames: blogCache.reviewedGames, recentGames: blogCache.recentGames };
    }

    console.log(...getLogPrefix(), 'fetching games...');
    fetchingGames = true;
    await fetchGames();
    fetchingGames = false;
    fetchedGames = true;

    console.log(...getLogPrefix(), 'done games...');
    return { reviewedGames: blogCache.reviewedGames, recentGames: blogCache.recentGames };
  }),
  fetchTiers: publicProcedure.query(async () => {
    if (fetchedTiers) {
      return { tierSets: blogCache.tierSets };
    }
    if (fetchingTiers) {
      return { tierSets: blogCache.tierSets };
    }

    console.log(...getLogPrefix(), 'fetching tiers...');
    fetchingTiers = true;
    await fetchTiers();
    fetchingTiers = false;
    fetchedTiers = true;

    console.log(...getLogPrefix(), 'done tiers...');
    return { tierSets: blogCache.tierSets };
  }),
});

export type BlogRouter = typeof blogRouter;
