import { type SelectQueryBuilder, sql } from 'kysely';
import { jsonArrayFrom, jsonObjectFrom } from 'kysely/helpers/postgres';
import * as _ from 'lodash-es';
import { z } from 'zod';

import { applyComputedGameSteam } from '@/database/gameSteam';
import { type KyselyDatabase, type KyselyInstance, kysely } from '@/database/kysely';
import { logQuery } from '@/helpers/utils';
import { SERVER_ENV } from '@/server/env';

import { createRouter, publicProcedure } from '../trpc';

export const filterSchema = z.object({
  search: z.string().optional(),
  filters: z
    .array(
      z.object({
        on: z.boolean().default(true),
        op: z.enum(['CONTAIN_SOME', 'CONTAIN_ALL']),
        incl: z.boolean().default(true),
        values: z.array(z.string()).default([]),
      }),
    )
    .default([]),
  sorts: z
    .array(
      z
        .object({
          order: z.union([z.literal(-1), z.literal(1)]).default(1),
        })
        .and(
          z.object({
            value: z.enum([
              'GAME:steamId',
              'GAME:gameName',
              'GAME:reviewPositive',
              'GAME:reviewTotal',
              'GAME:followerCount',
              'GAME:peakCount',
              'GAME:playingCount',
              'GAME:releaseDate',
              'GAME:createdAt',
              'GAME:updatedAt',
              'TOTAL_SCORE',
              'LATEST_REVIEW',
            ]),
          }),
        ),
    )
    .default([]),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().positive().default(100),
});

export const getGameSteamFullQuery = (input: z.infer<typeof filterSchema>, tx: KyselyInstance) => {
  const cleanInput = filterSchema.parse(input);
  const { search, filters, sorts } = cleanInput;
  // console.log('getGameSteamFullQuery', cleanInput);

  let query = tx.selectFrom('gameSteam as gs');

  if (filters.length) {
    filters.forEach(({ values, op, incl }) => {
      query = query.where(eb => {
        const getArrayCompareOp = () => {
          switch (op) {
            case 'CONTAIN_SOME':
              return '&&' as const;
            case 'CONTAIN_ALL':
              return '@>' as const;
          }
        };

        const getStringOp = () => {
          switch (op) {
            case 'CONTAIN_SOME':
              return 'in' as const;
            case 'CONTAIN_ALL':
              return 'in' as const;
          }
        };

        const getEbOp = () => {
          switch (op) {
            case 'CONTAIN_SOME':
              return eb.or;
            case 'CONTAIN_ALL':
            default:
              return eb.and;
          }
        };

        const ebOp = getEbOp();

        const groups = _.groupBy(
          values.map(value => {
            const [type, ...rest] = value.split(':');
            const key = rest.join(':');
            return { type, key };
          }),
          value => value.type,
        );

        const ebx = ebOp(
          Object.entries(groups).map(([type, values]) => {
            const keys = values.map(v => v.key);
            switch (type) {
              case 'SET':
                return eb.exists(
                  eb
                    .selectFrom(['tierLane as tl', 'tierItem as ti'])
                    .whereRef('tl.slug', '=', 'ti.tierLaneSlug')
                    .whereRef('ti.gameSteamId', '=', 'gs.steamId')
                    .where('tl.tierSetSlug', getStringOp(), keys)
                    .select('ti.gameSteamId'),
                );
              case 'LANE':
                return eb.exists(
                  eb
                    .selectFrom(['tierLane as tl', 'tierItem as ti'])
                    .whereRef('tl.slug', '=', 'ti.tierLaneSlug')
                    .whereRef('ti.gameSteamId', '=', 'gs.steamId')
                    .where('ti.tierLaneSlug', getStringOp(), keys)
                    .select('ti.gameSteamId'),
                );
              case 'HAS':
                return ebOp(
                  keys.map(k => {
                    switch (k) {
                      case 'steam_review':
                        return eb('gs.reviewTotal', '>', 0);
                      case 'steam_player':
                        return eb.or([eb('gs.peakCount', '>', 0), eb('gs.playingCount', '>', 0)]);
                      case 'demo':
                        return eb('gs.hasDemo', 'is', true);
                      case 'platform_win':
                        return eb('gs.platformWin', 'is', true);
                      case 'platform_mac':
                        return eb('gs.platformMac', 'is', true);
                      case 'platform_linux':
                        return eb('gs.platformLinux', 'is', true);
                      case 'review':
                        return eb.exists(
                          eb
                            .selectFrom('tierReview as tr')
                            .whereRef('tr.gameSteamId', '=', 'gs.steamId')
                            .where('tr.reviewTitle', '!=', '')
                            .select('tr.gameSteamId'),
                        );
                      default:
                        return eb.val(false);
                    }
                  }),
                );
              case 'PLAY_YEAR':
                return eb.exists(
                  eb
                    .selectFrom('tierItem as ti')
                    .whereRef('ti.gameSteamId', '=', 'gs.steamId')
                    .where('ti.tierLaneSlug', 'in', [
                      'suckz-s',
                      'suckz-u',
                      'suckz-c',
                      'suckz-k',
                      'suckz-z',
                    ])
                    .where(sql`extract(year from ${sql.ref('ti.createdAt')})`, 'in', keys)
                    .select('ti.gameSteamId'),
                );
              default:
                return eb.val(true);
            }
          }),
        );

        if (!incl) {
          return eb.not(ebx);
        }
        return ebx;
      });
    });
  }

  if (search) {
    query = query
      .innerJoin(
        sql<{
          similarity: number;
        }>`SIMILARITY(${search}, gs."gameName")`.as('similarity'),
        join => join.onTrue(),
      )
      .where(sql<any>`similarity > 0.5`)
      .orderBy('similarity', 'desc');
  }

  if (sorts.length) {
    sorts.forEach(sort => {
      if (sort.order) {
        const [type, ...rest] = sort.value.split(':');
        const key = rest.join(':');
        if (type === 'TOTAL_SCORE') {
          query = query.orderBy(
            eb =>
              eb
                .selectFrom(['tierItem as ti', 'tierLane as tl'])
                .whereRef('ti.gameSteamId', '=', 'gs.steamId')
                .whereRef('tl.slug', '=', 'ti.tierLaneSlug')
                .select(eb => eb.fn.sum('tl.score').as('score'))
                .limit(1),
            sort.order > 0 ? 'desc' : 'asc',
          );
        } else if (type === 'GAME') {
          query = query.orderBy(
            ({ ref }) => ref(key as keyof KyselyDatabase['gameSteam']),
            sql`${sql.raw(sort.order > 0 ? 'asc' : 'desc')} nulls last`,
          );
        } else if (type === 'LATEST_REVIEW') {
          query = query.orderBy(
            eb =>
              eb
                .selectFrom('tierItem as ti')
                .whereRef('ti.gameSteamId', '=', 'gs.steamId')
                .where('ti.tierLaneSlug', 'in', [
                  'suckz-s',
                  'suckz-u',
                  'suckz-c',
                  'suckz-k',
                  'suckz-z',
                ])
                .select('ti.updatedAt')
                .orderBy('ti.updatedAt', 'desc')
                .limit(1),
            sort.order > 0 ? 'desc' : 'asc',
          );
        }
      }
    });
  }

  query = query.orderBy('gs.createdAt', 'desc');

  return query;
};

function fillQueryWithSelect(
  query: SelectQueryBuilder<KyselyDatabase & { gs: KyselyDatabase['gameSteam'] }, 'gs', {}>,
) {
  return query.select(eb => [
    'gs.slug',
    'gs.steamId',
    'gs.gameName',
    'gs.gameUrl',
    'gs.developers',
    'gs.publishers',
    'gs.shortDescription',
    'gs.hasImageHeader',
    'gs.hasImageLibraryPoster',
    'gs.hasImageHeroCapsule',
    'gs.hasImagePageBgBlur',
    'gs.screenshots',
    'gs.movies',
    'gs.hasDemo',
    'gs.releaseDate',
    'gs.reviewPositive',
    'gs.reviewTotal',
    'gs.followerCount',
    'gs.platformWin',
    'gs.platformMac',
    'gs.platformLinux',
    'gs.peakCount',
    'gs.createdAt',
    jsonArrayFrom(
      eb
        .selectFrom('tierItem as ti')
        .whereRef('ti.gameSteamId', '=', 'gs.steamId')
        .select(['ti.id', 'ti.tierLaneSlug'])
        .orderBy('ti.id', 'asc'),
    ).as('tierItems'),
    jsonObjectFrom(
      eb.selectFrom('tierReview as tr').whereRef('tr.gameSteamId', '=', 'gs.steamId').selectAll(),
    ).as('tierReview'),
  ]);
}

export const gameSteamRouter = createRouter({
  list: publicProcedure.input(filterSchema).query(async ({ input }) => {
    const { skip, take } = input;

    const [result, count] = await kysely.transaction().execute(async tx => {
      const query = getGameSteamFullQuery(input, tx);
      const listQuery = fillQueryWithSelect(query).offset(skip).limit(take);
      const countQuery = query.clearOrderBy().select(({ fn }) => [fn.count('steamId').as('count')]);

      logQuery(listQuery);

      const [resResult, resCount] = await Promise.all([
        listQuery.execute(),
        countQuery.executeTakeFirst(),
      ]);

      return [resResult, Number(resCount?.count)];
    });

    const gameSteams = result.map(applyComputedGameSteam);

    const currentPage = Math.floor(skip / take) + 1;
    const totalPage = Math.ceil(count / take);

    console.log(
      `fetchGameSteams (${gameSteams.length}): ${currentPage}/${totalPage}`,
      gameSteams.slice(0, 3).map(item => ({ fullTitle: item.gameName, url: item.gameUrl })),
    );

    return {
      data: gameSteams,
      start: skip,
      end: skip + take,
      total: count,
      size: take,
      currentPage: currentPage,
      lastPage: totalPage,
    };
  }),
  search: publicProcedure
    .input(
      z.object({
        search: z.string().min(1),
      }),
    )
    .query(async ({ input, ctx: { user } }) => {
      const response = await fetch(
        'https://windmill.munverse.me/api/w/yay/jobs/run_wait_result/p/u/yay/search_game',
        {
          method: 'POST',
          headers: {
            'CF-Access-Client-Id': SERVER_ENV.CF_ACCESS_CLIENT_ID,
            'CF-Access-Client-Secret': SERVER_ENV.CF_ACCESS_CLIENT_SECRET,
            'Content-Type': 'application/json',
            Authorization: `Bearer ${SERVER_ENV.WINDMILL_TOKEN}`,
          },
          body: JSON.stringify({
            prompt: input.search,
          }),
        },
      );
      const searchResults: {
        gameUrl: string;
        gameName: string;
        steamId: number;
        similarity: number;
      }[] = await response.json();

      const searchResultIds = searchResults.map(s => s.steamId);

      const listQuery = fillQueryWithSelect(
        kysely
          .selectFrom('gameSteam as gs')
          .where(eb => eb('steamId', '=', eb.fn.any(eb.val(searchResultIds)))),
      );

      logQuery(listQuery);

      const listResult = await listQuery.execute();
      const computed = listResult.map(applyComputedGameSteam);
      const sorted = _.sortBy(computed, g => searchResultIds.indexOf(g.steamId));

      const count = sorted.length;

      return {
        data: sorted,
        start: 0,
        end: count,
        total: count,
        size: count,
        currentPage: 1,
        lastPage: 1,
      };
    }),
  refetch: publicProcedure
    .input(
      z.object({
        steamId: z.number(),
      }),
    )
    .mutation(async ({ input }) => {
      const { steamId } = input;

      // Call Windmill to refetch game data
      const response = await fetch('https://windmill.munverse.me/api/r/refetch_game', {
        method: 'POST',
        headers: {
          'CF-Access-Client-Id': SERVER_ENV.CF_ACCESS_CLIENT_ID,
          'CF-Access-Client-Secret': SERVER_ENV.CF_ACCESS_CLIENT_SECRET,
          'Content-Type': 'application/json',
          Authorization: `Bearer ${SERVER_ENV.WINDMILL_TOKEN}`,
        },
        body: JSON.stringify({
          games: [steamId],
          site: 'manual',
          skipExist: false,
          supabase: '$res:u/yay/supabase-omen',
        }),
      });

      // Check if the request was successful
      if (!response.ok) {
        throw new Error(`Failed to refetch game data: ${response.statusText}`, {
          cause: response,
        });
      }

      console.log('Refetch game data response:', await response.text());

      // Fetch the updated game from the database
      const listQuery = fillQueryWithSelect(
        kysely.selectFrom('gameSteam as gs').where('gs.steamId', '=', steamId),
      );

      const [result] = await listQuery.execute();
      const gameSteam = applyComputedGameSteam(result);

      return gameSteam;
    }),
});

export type GameSteamRouter = typeof gameSteamRouter;
