@import 'tailwindcss';
@plugin '@tailwindcss/typography';
@plugin '@tailwindcss/aspect-ratio';
@plugin 'tailwindcss-animated';
@plugin 'tailwindcss-primeui';

@config '../../tailwind.config.js';

@theme {
  --color-primary-50: 'rgb(var(--primary-50))';
  --color-primary-100: 'rgb(var(--primary-100))';
  --color-primary-200: 'rgb(var(--primary-200))';
  --color-primary-300: 'rgb(var(--primary-300))';
  --color-primary-400: 'rgb(var(--primary-400))';
  --color-primary-500: 'rgb(var(--primary-500))';
  --color-primary-600: 'rgb(var(--primary-600))';
  --color-primary-700: 'rgb(var(--primary-700))';
  --color-primary-800: 'rgb(var(--primary-800))';
  --color-primary-900: 'rgb(var(--primary-900))';
  --color-primary-950: 'rgb(var(--primary-950))';

  --color-surface-0: 'rgb(var(--surface-0))';
  --color-surface-50: 'rgb(var(--surface-50))';
  --color-surface-100: 'rgb(var(--surface-100))';
  --color-surface-200: 'rgb(var(--surface-200))';
  --color-surface-300: 'rgb(var(--surface-300))';
  --color-surface-400: 'rgb(var(--surface-400))';
  --color-surface-500: 'rgb(var(--surface-500))';
  --color-surface-600: 'rgb(var(--surface-600))';
  --color-surface-700: 'rgb(var(--surface-700))';
  --color-surface-800: 'rgb(var(--surface-800))';
  --color-surface-900: 'rgb(var(--surface-900))';
  --color-surface-950: 'rgb(var(--surface-950))';

  --color-theme-bg: var(--theme-bg);
  --color-theme-text: var(--theme-text);
  --color-theme-textlight: var(
    --theme-text-light
  );
  --color-theme-link: var(--theme-link);
  --color-theme-primary: var(--theme-primary);
  --color-theme-secondary: var(--theme-secondary);

  --radius-xl: calc(var(--radius) + 4px);
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-xs: calc(var(--radius) - 4px);

  /* For InspiraUI */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;

  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
  /* End For InspiraUI */
}

@layer base {
  :root {
    color-scheme: light;

    --primary-50: 253 249 237;
    --primary-100: 248 237 205;
    --primary-200: 240 217 151;
    --primary-300: 233 196 106;
    --primary-400: 226 171 61;
    --primary-500: 218 141 38;
    --primary-600: 193 108 30;
    --primary-700: 160 79 29;
    --primary-800: 131 62 29;
    --primary-900: 108 51 27;
    --primary-950: 61 25 11;

    --color-surface-0: 255 255 255;
    --color-surface-50: 244 247 247;
    --color-surface-100: 227 232 234;
    --color-surface-200: 203 212 214;
    --color-surface-300: 166 181 186;
    --color-surface-400: 122 143 150;
    --color-surface-500: 95 116 123;
    --color-surface-600: 82 98 104;
    --color-surface-700: 70 82 88;
    --color-surface-800: 63 71 75;
    --color-surface-900: 55 62 66;
    --color-surface-950: 34 39 42;

    --theme-text-rgb: 34 39 42;
    --theme-primary: #92400e;
    --theme-secondary: #064e3b;
    --theme-bg: #fafafa;
    --theme-link: #567c77;
    --theme-text: #22272a;
    --theme-text-light: #4b565c;
    --theme-quote: #cc2a42;
    --theme-menu-bg: rgb(250 250 250 / 0.85);
  }

  :root.dark {
    color-scheme: dark;

    --color-surface-0: 255 255 255;
    --color-surface-50: 250 250 250;
    --color-surface-100: 239 239 239;
    --color-surface-200: 220 220 220;
    --color-surface-300: 189 189 189;
    --color-surface-400: 152 152 152;
    --color-surface-500: 124 124 124;
    --color-surface-600: 101 101 101;
    --color-surface-700: 82 82 82;
    --color-surface-800: 70 70 70;
    --color-surface-900: 61 61 61;
    --color-surface-950: 41 41 41;

    --theme-text-rgb: 250 250 250;

    --theme-primary: #e9c46a;
    --theme-secondary: #2bbc89;
    --theme-bg: #1d1f21;
    --theme-link: #d480aa;
    --theme-text: #fafafa;
    --theme-text-light: #898989;
    --theme-quote: #ccffb6;
    --theme-menu-bg: rgb(29 31 33 / 0.85);

    /* For InspiraUI */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    /* End For InspiraUI */
  }

  html {
    @apply h-full scroll-smooth;
  }

  html body {
    @apply bg-theme-bg text-theme-text flex h-full flex-col px-8 pt-16 text-base font-normal antialiased;
  }
}
