<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { useVModels, watchDebounced } from '@vueuse/core';

import type { TierLaneForMainPage } from '@/database/tierSet';

import type { NewItemData } from './TheAddItem.vue';

const props = defineProps({
  newItemList: {
    type: Array as () => NewItemData[],
    required: true,
  },
  lane: {
    type: Object as () => TierLaneForMainPage,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:newItemList']);
const { newItemList } = useVModels(props, emit);

const adderGameSteamRaw = ref('');
const adderGameSteamCleaned = ref<URL[]>([]);

const runningId = ref(1);

const newItemError = reactive<
  Partial<Record<keyof (NewItemData & { type: 'GAME_STEAM' }), string>>
>({
  type: '',
  targetUrl: '',
});

watchDebounced(
  adderGameSteamRaw,
  val => {
    const urlStrs = val.split('\n');
    const validUrls: URL[] = [];
    urlStrs.forEach(str => {
      try {
        const url = new URL(str);
        if (url.host !== 'store.steampowered.com') {
          throw new Error('hostname not steam');
        }
        if (!/^\/app\/\d+\/?/.test(url.pathname)) {
          throw new Error('path not game');
        }
        if (
          [
            ...newItemList.value.map(x => 'targetUrl' in x && x.targetUrl),
            ...validUrls.map(x => x.href),
          ].find(x => x === url.href)
        ) {
          throw new Error('already exists');
        }

        url.protocol = 'https:';
        validUrls.push(url);
      } catch (err) {
        console.warn('Invalid URL:', str, err);
      }
    });
    adderGameSteamCleaned.value = validUrls;
  },
  { debounce: 400 },
);

function onAddGameSteam() {
  const items: NewItemData[] = adderGameSteamCleaned.value.map(
    url =>
      ({
        _id: ++runningId.value,
        type: 'GAME_STEAM',
        targetUrl: url.href,
        gameSteamId: +RegExp(/app\/(\d+)/).exec(url.pathname)![1],
        lexorank: props.lane.tierItems?.at(-1)?.lexorank ?? '',
      }) as NewItemData,
  );

  newItemList.value.push(...items);
  adderGameSteamCleaned.value.length = 0;
}
</script>

<template>
  <div class="flex-auto">
    <label for="targetUrl" class="mb-1 block font-bold">
      Steam URL <span class="text-theme-primary">*</span>
      <div class="text-sm font-light">(Can insert multiple, Split by lines)</div>
    </label>
    <div class="relative">
      <PrimeTextarea
        v-model="adderGameSteamRaw"
        inputId="targetUrl"
        :placeholder="
          [
            'https://store.steampowered.com/app/1092790/Inscryption/',
            'https://store.steampowered.com/app/105600/Terraria/',
            '......... must be valid steam urls! .........',
          ].join('\n')
        "
        :class="[
          'h-[30vh] text-sm!',
          { 'outline outline-2 outline-red-400/80': newItemError.targetUrl },
        ]"
      />
      <PrimeInlineMessage v-if="newItemError.targetUrl" severity="error">
        {{ newItemError.targetUrl }}
      </PrimeInlineMessage>
    </div>
    <PrimeButton
      :class="['group my-5', 'font-bold transition-all']"
      :disabled="!adderGameSteamCleaned.length"
      :loading="isLoading"
      @click="onAddGameSteam"
    >
      <Icon icon="lucide:plus" class="mr-1" />
      <span>
        Add
        <span class="font-black text-theme-secondary group-hover:text-theme-primary">
          {{ adderGameSteamCleaned.length }}
        </span>
        Games
      </span>
    </PrimeButton>
  </div>
</template>

<style scss="scss" scoped></style>
