<script setup lang="ts">
import { useElementBounding, useScroll, useWindowSize } from '@vueuse/core';

const containerRef = ref(null);
const isMobile = ref(false);

function updateIsMobile() {
  isMobile.value = window.innerWidth <= 768;
}

onMounted(() => {
  updateIsMobile();
  window.addEventListener('resize', updateIsMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateIsMobile);
});

const { height } = useWindowSize();
const { y: scrollY } = useScroll(window);
const { bottom } = useElementBounding(containerRef);

const scrollYProgress = computed(() => {
  if (!bottom.value) return 0;
  return 1 - Math.max(0, bottom.value - scrollY.value) / height.value;
});

const scaleDimensions = computed(() => (isMobile.value ? [0.7, 0.9] : [1.05, 1]));

const rotate = computed(() => 20 * (1 - scrollYProgress.value));
const scale = computed(() => {
  const [start, end] = scaleDimensions.value;
  return start + (end - start) * scrollYProgress.value;
});
const translateY = computed(() => -100 * scrollYProgress.value);
</script>

<template>
  <div ref="containerRef" class="relative flex items-center justify-center">
    <div class="relative w-full" style="perspective: 1000px">
      <slot :progress="scrollYProgress"></slot>
    </div>
  </div>
</template>
