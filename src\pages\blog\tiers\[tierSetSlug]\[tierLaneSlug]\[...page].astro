---
import ReviewBlogPost from '@/components/ReviewBlogPost.astro';
import ReviewBlogPostSmall from '@/components/ReviewBlogPostSmall.astro';
import TagLink from '@/components/TagLink.astro';
import Layout from '@/layouts/Layout.astro';
import { getServerTrpcCaller } from '@/server/caller';
import type { GetStaticPathsOptions } from 'astro';

export const prerender = true;

export const PAGE_SIZE = 1_000;

export async function getStaticPaths({ paginate }: GetStaticPathsOptions) {
  const { caller } = await getServerTrpcCaller(Astro, { prerender });
  const { tierSets } = await caller.blog.fetchTiers();

  return tierSets.flatMap(tierSet => {
    return tierSet.tierLanes.flatMap(tierLane => {
      const laneItems = tierLane.tierItems;
      const countReviewed = laneItems.filter(r => r.gameSteam?.isReviewed).length;
      const countPlayed = laneItems.filter(
        r => r.gameSteam?.isPlayed && !r.gameSteam?.isReviewed,
      ).length;
      const countSkipped = laneItems.filter(r => r.gameSteam?.isSkipped).length;

      return paginate(laneItems, {
        params: { tierSetSlug: tierLane.tierSetSlug, tierLaneSlug: tierLane.slug },
        props: {
          tierSet,
          tierLane,
          countPlayed,
          countReviewed,
          countSkipped,
        },
        pageSize: PAGE_SIZE,
      });
    });
  });
}

const { page, tierLane, tierSet, countReviewed, countPlayed, countSkipped } = Astro.props;

const TIER_1_COUNT = page.currentPage <= 1 ? 1 : 0;
const TIER_2_COUNT = page.currentPage <= 1 ? 31 : 0;

const tierItems1 = page.data.slice(0, TIER_1_COUNT);
const tierItems2 = page.data.slice(TIER_1_COUNT, TIER_2_COUNT);
const tierItems3 = page.data.slice(TIER_2_COUNT);
---

<Layout
  title={`${tierSet.label}: ${tierLane.label} (${page.currentPage}/${page.lastPage})`}
  description={`View all games with the ${tierSet.label} - ${tierLane.label}`}
  pagefindIgnore
>
  <h1 class="title mt-2 mb-6 flex items-center">
    <a href={`/blog/tiers/${tierSet.slug}`} class="text-primary sm:hover:underline">
      {tierSet.label}
    </a>
    <span class="ms-2 me-3">→</span>
    <span class=`text-xl criteria-${tierSet.slug} criteria-${tierSet.slug}-${tierLane.slug}`>
      <TagLink tierSetSlug={tierSet.slug} tierLane={tierLane} header noClick>
        {tierLane.label}
      </TagLink>
    </span>
    <span class="ml-1 text-xs text-slate-300">
      <span title="Reviewed" data-tooltip-placement="top" class="text-green-600">
        {countReviewed} R
      </span>
      <span title="Pending" data-tooltip-placement="top" class="text-yellow-600">
        {countPlayed} P
      </span>
      <span title="Skipped" data-tooltip-placement="top" class="text-slate-400">
        {countSkipped} S
      </span>
    </span>
  </h1>

  <section aria-label="Blog game list">
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {
        tierItems1.map(r => (
          <div class="game-item col-span-1 aspect-[4/3] sm:col-span-4">
            <ReviewBlogPost game={r.gameSteam as any} />
          </div>
        ))
      }
      {
        tierItems2.map(r => (
          <div class="game-item col-span-1 aspect-[4/3] sm:col-span-2">
            <ReviewBlogPost game={r.gameSteam as any} />
          </div>
        ))
      }
      {
        tierItems3.map(r => (
          <div class="game-item col-span-1 sm:col-span-1">
            <ReviewBlogPostSmall game={r.gameSteam as any} />
          </div>
        ))
      }
    </div>
  </section>
</Layout>
