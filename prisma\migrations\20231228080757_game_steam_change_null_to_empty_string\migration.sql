/*
 Warnings:
 
 - Made the column `gameNameEn` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `gameUrl` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `videoTeaser` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `videoTrailer` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `shortDescription` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `detailedDescription` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `aboutTheGame` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageLogo` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageCapsuleSm` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageCapsuleLg` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageHeader` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageHeroCapsule` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageLibraryPoster` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageLibraryHero` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imagePageBgBlur` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imagePageBgRaw` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageBroadcastLeftPanel` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `imageBroadcastRightPanel` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `ogImage` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 - Made the column `website` on table `gameSteam` required. This step will fail if there are existing NULL values in that column.
 
 */
-- Clear NULL
UPDATE
  "gameSteam"
SET
  "gameNameEn" = COALESCE("gameNameEn", ''),
  "gameUrl" = COALESCE("gameUrl", ''),
  "videoTeaser" = COALESCE("videoTeaser", ''),
  "videoTrailer" = COALESCE("videoTrailer", ''),
  "shortDescription" = COALESCE("shortDescription", ''),
  "detailedDescription" = COALESCE("detailedDescription", ''),
  "aboutTheGame" = COALESCE("aboutTheGame", ''),
  "imageLogo" = COALESCE("imageLogo", ''),
  "imageCapsuleSm" = COALESCE("imageCapsuleSm", ''),
  "imageCapsuleLg" = COALESCE("imageCapsuleLg", ''),
  "imageHeader" = COALESCE("imageHeader", ''),
  "imageHeroCapsule" = COALESCE("imageHeroCapsule", ''),
  "imageLibraryPoster" = COALESCE("imageLibraryPoster", ''),
  "imageLibraryHero" = COALESCE("imageLibraryHero", ''),
  "imagePageBgBlur" = COALESCE("imagePageBgBlur", ''),
  "imagePageBgRaw" = COALESCE("imagePageBgRaw", ''),
  "imageBroadcastLeftPanel" = COALESCE("imageBroadcastLeftPanel", ''),
  "imageBroadcastRightPanel" = COALESCE("imageBroadcastRightPanel", ''),
  "ogImage" = COALESCE("ogImage", ''),
  "website" = COALESCE("website", '')
WHERE
  "gameNameEn" IS NULL
  OR "gameUrl" IS NULL
  OR "videoTeaser" IS NULL
  OR "videoTrailer" IS NULL
  OR "shortDescription" IS NULL
  OR "detailedDescription" IS NULL
  OR "aboutTheGame" IS NULL
  OR "imageLogo" IS NULL
  OR "imageCapsuleSm" IS NULL
  OR "imageCapsuleLg" IS NULL
  OR "imageHeader" IS NULL
  OR "imageHeroCapsule" IS NULL
  OR "imageLibraryPoster" IS NULL
  OR "imageLibraryHero" IS NULL
  OR "imagePageBgBlur" IS NULL
  OR "imagePageBgRaw" IS NULL
  OR "imageBroadcastLeftPanel" IS NULL
  OR "imageBroadcastRightPanel" IS NULL
  OR "ogImage" IS NULL
  OR "website" IS NULL;

-- AlterTable
ALTER TABLE
  "gameSteam"
ADD
  COLUMN "hasDemo" BOOLEAN NOT NULL DEFAULT false,
ALTER COLUMN
  "gameName"
SET
  DEFAULT '',
ALTER COLUMN
  "gameNameEn"
SET
  NOT NULL,
ALTER COLUMN
  "gameNameEn"
SET
  DEFAULT '',
ALTER COLUMN
  "gameUrl"
SET
  NOT NULL,
ALTER COLUMN
  "gameUrl"
SET
  DEFAULT '',
ALTER COLUMN
  "slug"
SET
  DEFAULT '',
ALTER COLUMN
  "videoTeaser"
SET
  NOT NULL,
ALTER COLUMN
  "videoTeaser"
SET
  DEFAULT '',
ALTER COLUMN
  "videoTrailer"
SET
  NOT NULL,
ALTER COLUMN
  "videoTrailer"
SET
  DEFAULT '',
ALTER COLUMN
  "shortDescription"
SET
  NOT NULL,
ALTER COLUMN
  "shortDescription"
SET
  DEFAULT '',
ALTER COLUMN
  "detailedDescription"
SET
  NOT NULL,
ALTER COLUMN
  "detailedDescription"
SET
  DEFAULT '',
ALTER COLUMN
  "aboutTheGame"
SET
  NOT NULL,
ALTER COLUMN
  "aboutTheGame"
SET
  DEFAULT '',
ALTER COLUMN
  "imageLogo"
SET
  NOT NULL,
ALTER COLUMN
  "imageLogo"
SET
  DEFAULT '',
ALTER COLUMN
  "imageCapsuleSm"
SET
  NOT NULL,
ALTER COLUMN
  "imageCapsuleSm"
SET
  DEFAULT '',
ALTER COLUMN
  "imageCapsuleLg"
SET
  NOT NULL,
ALTER COLUMN
  "imageCapsuleLg"
SET
  DEFAULT '',
ALTER COLUMN
  "imageHeader"
SET
  NOT NULL,
ALTER COLUMN
  "imageHeader"
SET
  DEFAULT '',
ALTER COLUMN
  "imageHeroCapsule"
SET
  NOT NULL,
ALTER COLUMN
  "imageHeroCapsule"
SET
  DEFAULT '',
ALTER COLUMN
  "imageLibraryPoster"
SET
  NOT NULL,
ALTER COLUMN
  "imageLibraryPoster"
SET
  DEFAULT '',
ALTER COLUMN
  "imageLibraryHero"
SET
  NOT NULL,
ALTER COLUMN
  "imageLibraryHero"
SET
  DEFAULT '',
ALTER COLUMN
  "imagePageBgBlur"
SET
  NOT NULL,
ALTER COLUMN
  "imagePageBgBlur"
SET
  DEFAULT '',
ALTER COLUMN
  "imagePageBgRaw"
SET
  NOT NULL,
ALTER COLUMN
  "imagePageBgRaw"
SET
  DEFAULT '',
ALTER COLUMN
  "imageBroadcastLeftPanel"
SET
  NOT NULL,
ALTER COLUMN
  "imageBroadcastLeftPanel"
SET
  DEFAULT '',
ALTER COLUMN
  "imageBroadcastRightPanel"
SET
  NOT NULL,
ALTER COLUMN
  "imageBroadcastRightPanel"
SET
  DEFAULT '',
ALTER COLUMN
  "ogImage"
SET
  NOT NULL,
ALTER COLUMN
  "ogImage"
SET
  DEFAULT '',
ALTER COLUMN
  "website"
SET
  NOT NULL,
ALTER COLUMN
  "website"
SET
  DEFAULT '';