import { type ComputedPropertiesOfTierItem, applyComputedTierItem } from './tierItem';

export type ComputedPropertiesOfTierLane = {
  tierItems?: ComputedPropertiesOfTierItem[];
};

export function applyComputedTierLane<T extends object>(tierLane: T) {
  const applied: Omit<T, 'tierItems'> & ComputedPropertiesOfTierLane = tierLane;

  if (applied.tierItems) {
    applied.tierItems = applied.tierItems.map(applyComputedTierItem);
  }

  return applied;
}
