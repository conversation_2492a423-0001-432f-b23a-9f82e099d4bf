import Compressor from 'compressorjs';

export function compressImage(data: File | Blob) {
  return new Promise<File | undefined>(resolve => {
    return new Compressor(data, {
      quality: 0.6,
      // width: 500,
      // height: 500,
      maxWidth: 1000,
      maxHeight: 1000,
      convertSize: 100_000,
      success(compressed) {
        if (data.size === compressed.size) {
          resolve(undefined);
          return;
        }
        const filename = data instanceof File ? data.name.replace(/\.\w+$/, '.jpg') : 'image.jpg';
        console.log('[compressImage]:', filename, data.size, '->', compressed.size);
        if (compressed instanceof Blob) {
          resolve(new File([compressed], filename, { type: compressed.type }));
        } else {
          resolve(compressed);
        }
      },
      error(err) {
        console.error(err);
        resolve(undefined);
      },
    });
  });
}

export async function fileToBase64(file: File | Blob) {
  return new Promise<string | undefined>(resolve => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onloadend = () => {
      const base64data = reader.result;

      resolve(base64data?.toString());
    };
  });
}

export async function compressImageToBase64(data: File | Blob) {
  const compressed = await compressImage(data);

  if (!compressed) {
    return;
  }

  return fileToBase64(compressed);
}
