---
import TierSetGroup from '@/components/TierSetGroup.vue';
import Layout from '@/layouts/Layout.astro';
import { getServerTrpcCaller } from '@/server/caller';

export const prerender = false;

// export async function getStaticPaths() {
//   return [];
// }

function processTiers(str: string) {
  const parts = str.split('/');
  return parts;
}

const { tierParam } = Astro.params;
if (!tierParam) {
  return new Response(null, {
    status: 404,
    statusText: 'Not found',
  });
}

const slugs = processTiers(tierParam);

const { caller } = await getServerTrpcCaller(Astro);

const tierSets = await caller.tierSet.retrieveMany({
  slugs,
  tierLanes: {
    includeCount: true,
  },
  tierItems: {
    skip: 0,
    take: 100,
  },
});

if (tierSets.length === 0) {
  return new Response(null, {
    status: 404,
    statusText: 'Not found',
  });
}

let label = `${tierSets.map(t => t.label).join(', ')}`;

const filterLabel = tierSets
  .map(set => `▶ ${set.label}: ${set.tierLanes.map(lane => lane.label).join(', ')}`)
  .join(' & ');
if (filterLabel) {
  label += ` ${filterLabel.slice(0, 20)}...`;
}
---

<Layout title={label} showAuth isFullWidth>
  <div class="w-full">
    <!-- <h1 class="mb-4 text-xl font-black">{label}</h1> -->
    <TierSetGroup tierSets={tierSets} client:load />
  </div>
</Layout>

<style></style>
