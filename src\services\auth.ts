import { supabase } from '@/database/supabase.browser';

export async function signInWithGoogle() {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/api/auth/callback`,
      },
    });
    if (data) {
      return data;
    }
    if (error) {
      throw error;
    }
  } catch (e) {
    console.error('[ERR] signInWithGoogle', e);
  }
}
export async function signInWithDiscord() {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'discord',
      options: {
        redirectTo: `${window.location.origin}/api/auth/callback`,
      },
    });
    if (data) {
      return data;
    }
    if (error) {
      throw error;
    }
  } catch (e) {
    console.error('[ERR] signInWithDiscord', e);
  }
}

export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      throw error;
    }
    window.location.reload();
    return true;
  } catch (e) {
    console.error('[ERR] signOut', e);
  }
  return false;
}
