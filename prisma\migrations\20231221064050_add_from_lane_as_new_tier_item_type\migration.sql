/*
  Warnings:

  - You are about to drop the `fromLaneToLane` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
ALTER TYPE "TierItemType" ADD VALUE 'FROM_LANE';

-- DropForeignKey
ALTER TABLE "fromLaneToLane" DROP CONSTRAINT "fromLaneToLane_fromLaneId_fkey";

-- DropForeignKey
ALTER TABLE "fromLaneToLane" DROP CONSTRAINT "fromLaneToLane_toLaneId_fkey";

-- AlterTable
ALTER TABLE "tierItem" ADD COLUMN     "fromLaneSlug" TEXT,
ADD COLUMN     "fromLaneTierSetSlug" TEXT;

-- DropTable
DROP TABLE "fromLaneToLane";

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_fromLaneTierSetSlug_fromLaneSlug_fkey" FOREIGN KEY ("fromLaneTierSetSlug", "fromLaneSlug") REFERENCES "tierLane"("tierSetSlug", "slug") ON DELETE SET NULL ON UPDATE CASCADE;
