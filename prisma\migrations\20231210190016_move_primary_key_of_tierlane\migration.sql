/*
  Warnings:

  - The primary key for the `tierLane` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[tierSetSlug,slug]` on the table `tierLane` will be added. If there are existing duplicate values, this will fail.

*/
-- DropF<PERSON>ignKey
ALTER TABLE "tierItem" DROP CONSTRAINT "tierItem_tierLaneSlug_fkey";

-- AlterTable
ALTER TABLE "tierItem" ADD COLUMN     "tierLaneTierSetSlug" TEXT;

-- AlterTable
ALTER TABLE "tierLane" DROP CONSTRAINT "tierLane_pkey",
ADD CONSTRAINT "tierLane_pkey" PRIMARY KEY ("tierSetSlug", "slug");

-- CreateIndex
CREATE UNIQUE INDEX "tierLane_tierSetSlug_slug_key" ON "tierLane"("tierSetSlug", "slug");

-- AddForeignKey
ALTER TABLE "tierItem" ADD CONSTRAINT "tierItem_tierLaneTierSetSlug_tierLaneSlug_fkey" FOREIGN KEY ("tierLaneTierSetSlug", "tierLaneSlug") REFERENCES "tierLane"("tierSetSlug", "slug") ON DELETE SET NULL ON UPDATE CASCADE;
