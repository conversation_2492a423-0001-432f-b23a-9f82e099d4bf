import { z } from 'zod';

import { type KyselyInstance, kysely } from '@/database/kysely';
import { SERVER_ENV } from '@/server/env';

import { createRouter, publicProcedure } from '../trpc';

const CraftedItemSchema = z.object({
  result_item: z.string(),
  description: z.string(),
  emojis: z.string(),
  color: z.string(),
  reason: z.string(),
});

export type CraftedItem = z.infer<typeof CraftedItemSchema>;

export const craftRouter = createRouter({
  // ---------------------------
  request: publicProcedure
    .input(
      z.object({
        items: z.array(z.string()).length(9),
        temperature: z.number().min(0).max(1).default(0.8),
      }),
    )
    .query(async ({ input: { items, temperature }, ctx: { user } }) => {
      const RED_CARD = {
        result_item: 'ใบแดง',
        description: 'ใบสั่งห้ามคนแปลกหน้า',
        emojis: '🤕',
        color: '#f00',
        reason: 'คุณไม่มีสิทธิ์',
      };

      if (!user) {
        return RED_CARD;
      }

      const isUserInMungamerSpace = await kysely
        .selectFrom('space as sp')
        .select('slug')
        .where('sp.slug', '=', 'mungamer')
        .where(eb =>
          eb.or([
            eb(
              eb.selectFrom('player as p').where('p.userId', '=', user.id).select('p.id'),
              'in',
              eb
                .selectFrom('playersOnSpaces as pos')
                .whereRef('pos.spaceId', '=', 'sp.id')
                .select('pos.playerId'),
            ),
          ]),
        )
        .executeTakeFirst();

      if (!isUserInMungamerSpace) {
        return RED_CARD;
      }

      const payload = { items: items.map(item => item.trim()), temperature };

      const response = await fetch('https://windmill.munverse.me/api/r/craft', {
        method: 'POST',
        headers: {
          'CF-Access-Client-Id': SERVER_ENV.CF_ACCESS_CLIENT_ID,
          'CF-Access-Client-Secret': SERVER_ENV.CF_ACCESS_CLIENT_SECRET,
          'Content-Type': 'application/json',
          Authorization: `Bearer ${SERVER_ENV.WINDMILL_TOKEN}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API call failed with status: ${response.status}. Body: ${errorBody}`);
      }

      const result: CraftedItem = await response.json();
      return result;
    }),
});

export type CraftRouter = typeof craftRouter;
