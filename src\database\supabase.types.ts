export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  pgbouncer: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_auth: {
        Args: {
          p_usename: string;
        };
        Returns: {
          username: string;
          password: string;
        }[];
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      _prisma_migrations: {
        Row: {
          applied_steps_count: number;
          checksum: string;
          finished_at: string | null;
          id: string;
          logs: string | null;
          migration_name: string;
          rolled_back_at: string | null;
          started_at: string;
        };
        Insert: {
          applied_steps_count?: number;
          checksum: string;
          finished_at?: string | null;
          id: string;
          logs?: string | null;
          migration_name: string;
          rolled_back_at?: string | null;
          started_at?: string;
        };
        Update: {
          applied_steps_count?: number;
          checksum?: string;
          finished_at?: string | null;
          id?: string;
          logs?: string | null;
          migration_name?: string;
          rolled_back_at?: string | null;
          started_at?: string;
        };
        Relationships: [];
      };
      filterPreset: {
        Row: {
          createdAt: string;
          filters: Json;
          id: number;
          name: string;
          public: boolean;
          sorts: Json;
          updatedAt: string;
          userId: string;
        };
        Insert: {
          createdAt?: string;
          filters?: Json;
          id?: number;
          name: string;
          public?: boolean;
          sorts?: Json;
          updatedAt?: string;
          userId: string;
        };
        Update: {
          createdAt?: string;
          filters?: Json;
          id?: number;
          name?: string;
          public?: boolean;
          sorts?: Json;
          updatedAt?: string;
          userId?: string;
        };
        Relationships: [];
      };
      gameSteam: {
        Row: {
          aboutTheGame: string;
          awards: string[];
          createdAt: string;
          demoId: number | null;
          detailedDescription: string;
          developers: string[];
          followerCount: number | null;
          gameName: string;
          gameNameEn: string;
          gameTags: string[];
          gameUrl: string;
          hasAgeCheck: boolean;
          hasDemo: boolean;
          hasImageBroadcastLeftPanel: boolean;
          hasImageBroadcastRightPanel: boolean;
          hasImageCapsuleLg: boolean;
          hasImageCapsuleSm: boolean;
          hasImageHeader: boolean;
          hasImageHeroCapsule: boolean;
          hasImageLibraryHero: boolean;
          hasImageLibraryPoster: boolean;
          hasImageLogo: boolean;
          hasImagePageBgBlur: boolean;
          hasImagePageBgRaw: boolean;
          id: number;
          isAvailable: boolean;
          movies: Json[];
          nominations: string[];
          peakCount: number | null;
          platformLinux: boolean | null;
          platformMac: boolean | null;
          platformWin: boolean | null;
          playingCount: number | null;
          publishers: string[];
          recordedVideo: string[];
          releaseDate: string | null;
          reviewPositive: number | null;
          reviewTotal: number | null;
          screenshots: Json[];
          shortDescription: string;
          slug: string;
          steamId: number;
          totalScore: number;
          updatedAt: string;
          website: string;
        };
        Insert: {
          aboutTheGame?: string;
          awards?: string[];
          createdAt?: string;
          demoId?: number | null;
          detailedDescription?: string;
          developers?: string[];
          followerCount?: number | null;
          gameName?: string;
          gameNameEn?: string;
          gameTags?: string[];
          gameUrl?: string;
          hasAgeCheck?: boolean;
          hasDemo?: boolean;
          hasImageBroadcastLeftPanel?: boolean;
          hasImageBroadcastRightPanel?: boolean;
          hasImageCapsuleLg?: boolean;
          hasImageCapsuleSm?: boolean;
          hasImageHeader?: boolean;
          hasImageHeroCapsule?: boolean;
          hasImageLibraryHero?: boolean;
          hasImageLibraryPoster?: boolean;
          hasImageLogo?: boolean;
          hasImagePageBgBlur?: boolean;
          hasImagePageBgRaw?: boolean;
          id?: number;
          isAvailable?: boolean;
          movies?: Json[];
          nominations?: string[];
          peakCount?: number | null;
          platformLinux?: boolean | null;
          platformMac?: boolean | null;
          platformWin?: boolean | null;
          playingCount?: number | null;
          publishers?: string[];
          recordedVideo?: string[];
          releaseDate?: string | null;
          reviewPositive?: number | null;
          reviewTotal?: number | null;
          screenshots?: Json[];
          shortDescription?: string;
          slug?: string;
          steamId: number;
          totalScore?: number;
          updatedAt?: string;
          website?: string;
        };
        Update: {
          aboutTheGame?: string;
          awards?: string[];
          createdAt?: string;
          demoId?: number | null;
          detailedDescription?: string;
          developers?: string[];
          followerCount?: number | null;
          gameName?: string;
          gameNameEn?: string;
          gameTags?: string[];
          gameUrl?: string;
          hasAgeCheck?: boolean;
          hasDemo?: boolean;
          hasImageBroadcastLeftPanel?: boolean;
          hasImageBroadcastRightPanel?: boolean;
          hasImageCapsuleLg?: boolean;
          hasImageCapsuleSm?: boolean;
          hasImageHeader?: boolean;
          hasImageHeroCapsule?: boolean;
          hasImageLibraryHero?: boolean;
          hasImageLibraryPoster?: boolean;
          hasImageLogo?: boolean;
          hasImagePageBgBlur?: boolean;
          hasImagePageBgRaw?: boolean;
          id?: number;
          isAvailable?: boolean;
          movies?: Json[];
          nominations?: string[];
          peakCount?: number | null;
          platformLinux?: boolean | null;
          platformMac?: boolean | null;
          platformWin?: boolean | null;
          playingCount?: number | null;
          publishers?: string[];
          recordedVideo?: string[];
          releaseDate?: string | null;
          reviewPositive?: number | null;
          reviewTotal?: number | null;
          screenshots?: Json[];
          shortDescription?: string;
          slug?: string;
          steamId?: number;
          totalScore?: number;
          updatedAt?: string;
          website?: string;
        };
        Relationships: [];
      };
      player: {
        Row: {
          createdAt: string;
          displayName: string;
          id: number;
          updatedAt: string;
          userId: string;
        };
        Insert: {
          createdAt?: string;
          displayName?: string;
          id?: number;
          updatedAt?: string;
          userId: string;
        };
        Update: {
          createdAt?: string;
          displayName?: string;
          id?: number;
          updatedAt?: string;
          userId?: string;
        };
        Relationships: [];
      };
      playersOnSpaces: {
        Row: {
          assignedBy: string | null;
          createdAt: string;
          playerId: number;
          spaceId: number;
          updatedAt: string;
        };
        Insert: {
          assignedBy?: string | null;
          createdAt?: string;
          playerId: number;
          spaceId: number;
          updatedAt?: string;
        };
        Update: {
          assignedBy?: string | null;
          createdAt?: string;
          playerId?: number;
          spaceId?: number;
          updatedAt?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'playersOnSpaces_playerId_fkey';
            columns: ['playerId'];
            referencedRelation: 'player';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'playersOnSpaces_spaceId_fkey';
            columns: ['spaceId'];
            referencedRelation: 'space';
            referencedColumns: ['id'];
          },
        ];
      };
      simpleTier: {
        Row: {
          createdAt: string;
          id: number;
          jsonObject: Json | null;
          label: string;
          lexorank: string;
          spaceId: number | null;
          updatedAt: string;
        };
        Insert: {
          createdAt?: string;
          id?: number;
          jsonObject?: Json | null;
          label?: string;
          lexorank?: string;
          spaceId?: number | null;
          updatedAt?: string;
        };
        Update: {
          createdAt?: string;
          id?: number;
          jsonObject?: Json | null;
          label?: string;
          lexorank?: string;
          spaceId?: number | null;
          updatedAt?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'simpleTier_spaceId_fkey';
            columns: ['spaceId'];
            referencedRelation: 'space';
            referencedColumns: ['id'];
          },
        ];
      };
      space: {
        Row: {
          createdAt: string;
          id: number;
          label: string;
          slug: string;
          updatedAt: string;
        };
        Insert: {
          createdAt?: string;
          id?: number;
          label?: string;
          slug: string;
          updatedAt?: string;
        };
        Update: {
          createdAt?: string;
          id?: number;
          label?: string;
          slug?: string;
          updatedAt?: string;
        };
        Relationships: [];
      };
      tierItem: {
        Row: {
          createdAt: string;
          gameSteamId: number;
          id: number;
          lexorank: string | null;
          publishDate: string | null;
          reviewContent: string;
          reviewTitle: string;
          tierLaneSlug: string;
          updatedAt: string;
        };
        Insert: {
          createdAt?: string;
          gameSteamId: number;
          id?: number;
          lexorank?: string | null;
          publishDate?: string | null;
          reviewContent?: string;
          reviewTitle?: string;
          tierLaneSlug: string;
          updatedAt?: string;
        };
        Update: {
          createdAt?: string;
          gameSteamId?: number;
          id?: number;
          lexorank?: string | null;
          publishDate?: string | null;
          reviewContent?: string;
          reviewTitle?: string;
          tierLaneSlug?: string;
          updatedAt?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tierItem_gameSteamId_fkey';
            columns: ['gameSteamId'];
            referencedRelation: 'gameSteam';
            referencedColumns: ['steamId'];
          },
          {
            foreignKeyName: 'tierItem_tierLaneSlug_fkey';
            columns: ['tierLaneSlug'];
            referencedRelation: 'tierLane';
            referencedColumns: ['slug'];
          },
        ];
      };
      tierLane: {
        Row: {
          createdAt: string;
          description: string;
          icon: string;
          label: string;
          mainColor: string;
          score: number;
          slug: string;
          textColor: string;
          tierSetSlug: string;
          updatedAt: string;
        };
        Insert: {
          createdAt?: string;
          description?: string;
          icon?: string;
          label?: string;
          mainColor?: string;
          score?: number;
          slug: string;
          textColor?: string;
          tierSetSlug: string;
          updatedAt?: string;
        };
        Update: {
          createdAt?: string;
          description?: string;
          icon?: string;
          label?: string;
          mainColor?: string;
          score?: number;
          slug?: string;
          textColor?: string;
          tierSetSlug?: string;
          updatedAt?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tagOne_tagGroupSlug_fkey';
            columns: ['tierSetSlug'];
            referencedRelation: 'tierSet';
            referencedColumns: ['slug'];
          },
        ];
      };
      tierReview: {
        Row: {
          createdAt: string;
          gameSteamId: number;
          publishDate: string | null;
          reviewContent: string | null;
          reviewTitle: string | null;
          scoreAudio: number | null;
          scoreBias: number | null;
          scoreBuy: number | null;
          scoreControl: number | null;
          scoreCreative: number | null;
          scoreFun: number | null;
          scoreGraphic: number | null;
          scorePotential: number | null;
          scoreStability: number | null;
          scoreSuckz: number | null;
          scoreUi: number | null;
          updatedAt: string;
        };
        Insert: {
          createdAt?: string;
          gameSteamId: number;
          publishDate?: string | null;
          reviewContent?: string | null;
          reviewTitle?: string | null;
          scoreAudio?: number | null;
          scoreBias?: number | null;
          scoreBuy?: number | null;
          scoreControl?: number | null;
          scoreCreative?: number | null;
          scoreFun?: number | null;
          scoreGraphic?: number | null;
          scorePotential?: number | null;
          scoreStability?: number | null;
          scoreSuckz?: number | null;
          scoreUi?: number | null;
          updatedAt?: string;
        };
        Update: {
          createdAt?: string;
          gameSteamId?: number;
          publishDate?: string | null;
          reviewContent?: string | null;
          reviewTitle?: string | null;
          scoreAudio?: number | null;
          scoreBias?: number | null;
          scoreBuy?: number | null;
          scoreControl?: number | null;
          scoreCreative?: number | null;
          scoreFun?: number | null;
          scoreGraphic?: number | null;
          scorePotential?: number | null;
          scoreStability?: number | null;
          scoreSuckz?: number | null;
          scoreUi?: number | null;
          updatedAt?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tierReview_gameSteamId_fkey';
            columns: ['gameSteamId'];
            referencedRelation: 'gameSteam';
            referencedColumns: ['steamId'];
          },
        ];
      };
      tierSet: {
        Row: {
          createdAt: string;
          description: string;
          icon: string;
          label: string;
          permissionType: Database['public']['Enums']['PermissionType'];
          slug: string;
          spaceId: number | null;
          updatedAt: string;
        };
        Insert: {
          createdAt?: string;
          description?: string;
          icon?: string;
          label?: string;
          permissionType?: Database['public']['Enums']['PermissionType'];
          slug: string;
          spaceId?: number | null;
          updatedAt?: string;
        };
        Update: {
          createdAt?: string;
          description?: string;
          icon?: string;
          label?: string;
          permissionType?: Database['public']['Enums']['PermissionType'];
          slug?: string;
          spaceId?: number | null;
          updatedAt?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'group_spaceId_fkey';
            columns: ['spaceId'];
            referencedRelation: 'space';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      dmetaphone: {
        Args: {
          '': string;
        };
        Returns: string;
      };
      dmetaphone_alt: {
        Args: {
          '': string;
        };
        Returns: string;
      };
      gtrgm_compress: {
        Args: {
          '': unknown;
        };
        Returns: unknown;
      };
      gtrgm_decompress: {
        Args: {
          '': unknown;
        };
        Returns: unknown;
      };
      gtrgm_in: {
        Args: {
          '': unknown;
        };
        Returns: unknown;
      };
      gtrgm_options: {
        Args: {
          '': unknown;
        };
        Returns: undefined;
      };
      gtrgm_out: {
        Args: {
          '': unknown;
        };
        Returns: unknown;
      };
      hybrid_search: {
        Args: {
          query_text: string;
          query_embedding: string;
          match_count: number;
          full_text_weight?: number;
          semantic_weight?: number;
          rrf_k?: number;
        };
        Returns: {
          aboutTheGame: string;
          awards: string[];
          createdAt: string;
          demoId: number | null;
          detailedDescription: string;
          developers: string[];
          followerCount: number | null;
          gameName: string;
          gameNameEn: string;
          gameTags: string[];
          gameUrl: string;
          hasAgeCheck: boolean;
          hasDemo: boolean;
          hasImageBroadcastLeftPanel: boolean;
          hasImageBroadcastRightPanel: boolean;
          hasImageCapsuleLg: boolean;
          hasImageCapsuleSm: boolean;
          hasImageHeader: boolean;
          hasImageHeroCapsule: boolean;
          hasImageLibraryHero: boolean;
          hasImageLibraryPoster: boolean;
          hasImageLogo: boolean;
          hasImagePageBgBlur: boolean;
          hasImagePageBgRaw: boolean;
          id: number;
          isAvailable: boolean;
          movies: Json[];
          nominations: string[];
          peakCount: number | null;
          platformLinux: boolean | null;
          platformMac: boolean | null;
          platformWin: boolean | null;
          playingCount: number | null;
          publishers: string[];
          recordedVideo: string[];
          releaseDate: string | null;
          reviewPositive: number | null;
          reviewTotal: number | null;
          screenshots: Json[];
          shortDescription: string;
          slug: string;
          steamId: number;
          totalScore: number;
          updatedAt: string;
          website: string;
        }[];
      };
      kw_match_games: {
        Args: {
          query_text: string;
          match_count: number;
        };
        Returns: {
          id: number;
          metadata: Json;
          similarity: number;
        }[];
      };
      match_games: {
        Args: {
          query_embedding: string;
          match_count?: number;
          filter?: Json;
        };
        Returns: {
          id: number;
          metadata: Json;
          similarity: number;
        }[];
      };
      match_page_sections: {
        Args: {
          embedding: string;
          match_threshold: number;
          match_count: number;
        };
        Returns: {
          steamid: number;
          gamename: string;
          gameurl: string;
          shortdescription: string;
          similarity: number;
        }[];
      };
      search_game: {
        Args: {
          p_tier_set_slug: string;
          p_tier_set_bypass: boolean;
          p_tier_lane_slug: string;
          p_tier_lane_bypass: boolean;
          p_search_query: string;
        };
        Returns: {
          id: number;
          gamename: string;
          rank: number;
          ranka: number;
          rankb: number;
        }[];
      };
      search_game_by_keyword: {
        Args: {
          p_search_query: string;
        };
        Returns: {
          r_id: number;
          r_gamename: string;
          r_rank: number;
          r_similarity: number;
        }[];
      };
      set_limit: {
        Args: {
          '': number;
        };
        Returns: number;
      };
      show_limit: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      show_trgm: {
        Args: {
          '': string;
        };
        Returns: string[];
      };
      soundex: {
        Args: {
          '': string;
        };
        Returns: string;
      };
      text_soundex: {
        Args: {
          '': string;
        };
        Returns: string;
      };
      update_game_steam_tsv: {
        Args: {
          p_id: number;
        };
        Returns: undefined;
      };
      update_tier_item_tsv: {
        Args: {
          p_id: number;
        };
        Returns: undefined;
      };
    };
    Enums: {
      PermissionType: 'PRIVATE' | 'PUBLIC';
      TierItemType: 'NORMAL' | 'GAME_STEAM' | 'FROM_LANE';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null;
          avif_autodetection: boolean | null;
          created_at: string | null;
          file_size_limit: number | null;
          id: string;
          name: string;
          owner: string | null;
          owner_id: string | null;
          public: boolean | null;
          type: Database['storage']['Enums']['buckettype'];
          updated_at: string | null;
        };
        Insert: {
          allowed_mime_types?: string[] | null;
          avif_autodetection?: boolean | null;
          created_at?: string | null;
          file_size_limit?: number | null;
          id: string;
          name: string;
          owner?: string | null;
          owner_id?: string | null;
          public?: boolean | null;
          type?: Database['storage']['Enums']['buckettype'];
          updated_at?: string | null;
        };
        Update: {
          allowed_mime_types?: string[] | null;
          avif_autodetection?: boolean | null;
          created_at?: string | null;
          file_size_limit?: number | null;
          id?: string;
          name?: string;
          owner?: string | null;
          owner_id?: string | null;
          public?: boolean | null;
          type?: Database['storage']['Enums']['buckettype'];
          updated_at?: string | null;
        };
        Relationships: [];
      };
      buckets_analytics: {
        Row: {
          created_at: string;
          format: string;
          id: string;
          type: Database['storage']['Enums']['buckettype'];
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          format?: string;
          id: string;
          type?: Database['storage']['Enums']['buckettype'];
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          format?: string;
          id?: string;
          type?: Database['storage']['Enums']['buckettype'];
          updated_at?: string;
        };
        Relationships: [];
      };
      iceberg_namespaces: {
        Row: {
          bucket_id: string;
          created_at: string;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          bucket_id: string;
          created_at?: string;
          id?: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          bucket_id?: string;
          created_at?: string;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'iceberg_namespaces_bucket_id_fkey';
            columns: ['bucket_id'];
            referencedRelation: 'buckets_analytics';
            referencedColumns: ['id'];
          },
        ];
      };
      iceberg_tables: {
        Row: {
          bucket_id: string;
          created_at: string;
          id: string;
          location: string;
          name: string;
          namespace_id: string;
          updated_at: string;
        };
        Insert: {
          bucket_id: string;
          created_at?: string;
          id?: string;
          location: string;
          name: string;
          namespace_id: string;
          updated_at?: string;
        };
        Update: {
          bucket_id?: string;
          created_at?: string;
          id?: string;
          location?: string;
          name?: string;
          namespace_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'iceberg_tables_bucket_id_fkey';
            columns: ['bucket_id'];
            referencedRelation: 'buckets_analytics';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'iceberg_tables_namespace_id_fkey';
            columns: ['namespace_id'];
            referencedRelation: 'iceberg_namespaces';
            referencedColumns: ['id'];
          },
        ];
      };
      migrations: {
        Row: {
          executed_at: string | null;
          hash: string;
          id: number;
          name: string;
        };
        Insert: {
          executed_at?: string | null;
          hash: string;
          id: number;
          name: string;
        };
        Update: {
          executed_at?: string | null;
          hash?: string;
          id?: number;
          name?: string;
        };
        Relationships: [];
      };
      objects: {
        Row: {
          bucket_id: string | null;
          created_at: string | null;
          id: string;
          last_accessed_at: string | null;
          level: number | null;
          metadata: Json | null;
          name: string | null;
          owner: string | null;
          owner_id: string | null;
          path_tokens: string[] | null;
          updated_at: string | null;
          user_metadata: Json | null;
          version: string | null;
        };
        Insert: {
          bucket_id?: string | null;
          created_at?: string | null;
          id?: string;
          last_accessed_at?: string | null;
          level?: number | null;
          metadata?: Json | null;
          name?: string | null;
          owner?: string | null;
          owner_id?: string | null;
          path_tokens?: string[] | null;
          updated_at?: string | null;
          user_metadata?: Json | null;
          version?: string | null;
        };
        Update: {
          bucket_id?: string | null;
          created_at?: string | null;
          id?: string;
          last_accessed_at?: string | null;
          level?: number | null;
          metadata?: Json | null;
          name?: string | null;
          owner?: string | null;
          owner_id?: string | null;
          path_tokens?: string[] | null;
          updated_at?: string | null;
          user_metadata?: Json | null;
          version?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'objects_bucketId_fkey';
            columns: ['bucket_id'];
            referencedRelation: 'buckets';
            referencedColumns: ['id'];
          },
        ];
      };
      prefixes: {
        Row: {
          bucket_id: string;
          created_at: string | null;
          level: number;
          name: string;
          updated_at: string | null;
        };
        Insert: {
          bucket_id: string;
          created_at?: string | null;
          level?: number;
          name: string;
          updated_at?: string | null;
        };
        Update: {
          bucket_id?: string;
          created_at?: string | null;
          level?: number;
          name?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'prefixes_bucketId_fkey';
            columns: ['bucket_id'];
            referencedRelation: 'buckets';
            referencedColumns: ['id'];
          },
        ];
      };
      s3_multipart_uploads: {
        Row: {
          bucket_id: string;
          created_at: string;
          id: string;
          in_progress_size: number;
          key: string;
          owner_id: string | null;
          upload_signature: string;
          user_metadata: Json | null;
          version: string;
        };
        Insert: {
          bucket_id: string;
          created_at?: string;
          id: string;
          in_progress_size?: number;
          key: string;
          owner_id?: string | null;
          upload_signature: string;
          user_metadata?: Json | null;
          version: string;
        };
        Update: {
          bucket_id?: string;
          created_at?: string;
          id?: string;
          in_progress_size?: number;
          key?: string;
          owner_id?: string | null;
          upload_signature?: string;
          user_metadata?: Json | null;
          version?: string;
        };
        Relationships: [
          {
            foreignKeyName: 's3_multipart_uploads_bucket_id_fkey';
            columns: ['bucket_id'];
            referencedRelation: 'buckets';
            referencedColumns: ['id'];
          },
        ];
      };
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string;
          created_at: string;
          etag: string;
          id: string;
          key: string;
          owner_id: string | null;
          part_number: number;
          size: number;
          upload_id: string;
          version: string;
        };
        Insert: {
          bucket_id: string;
          created_at?: string;
          etag: string;
          id?: string;
          key: string;
          owner_id?: string | null;
          part_number: number;
          size?: number;
          upload_id: string;
          version: string;
        };
        Update: {
          bucket_id?: string;
          created_at?: string;
          etag?: string;
          id?: string;
          key?: string;
          owner_id?: string | null;
          part_number?: number;
          size?: number;
          upload_id?: string;
          version?: string;
        };
        Relationships: [
          {
            foreignKeyName: 's3_multipart_uploads_parts_bucket_id_fkey';
            columns: ['bucket_id'];
            referencedRelation: 'buckets';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 's3_multipart_uploads_parts_upload_id_fkey';
            columns: ['upload_id'];
            referencedRelation: 's3_multipart_uploads';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      add_prefixes: {
        Args: {
          _bucket_id: string;
          _name: string;
        };
        Returns: undefined;
      };
      can_insert_object: {
        Args: {
          bucketid: string;
          name: string;
          owner: string;
          metadata: Json;
        };
        Returns: undefined;
      };
      delete_prefix: {
        Args: {
          _bucket_id: string;
          _name: string;
        };
        Returns: boolean;
      };
      extension: {
        Args: {
          name: string;
        };
        Returns: string;
      };
      filename: {
        Args: {
          name: string;
        };
        Returns: string;
      };
      foldername: {
        Args: {
          name: string;
        };
        Returns: string[];
      };
      get_level: {
        Args: {
          name: string;
        };
        Returns: number;
      };
      get_prefix: {
        Args: {
          name: string;
        };
        Returns: string;
      };
      get_prefixes: {
        Args: {
          name: string;
        };
        Returns: string[];
      };
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>;
        Returns: {
          size: number;
          bucket_id: string;
        }[];
      };
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string;
          prefix_param: string;
          delimiter_param: string;
          max_keys?: number;
          next_key_token?: string;
          next_upload_token?: string;
        };
        Returns: {
          key: string;
          id: string;
          created_at: string;
        }[];
      };
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string;
          prefix_param: string;
          delimiter_param: string;
          max_keys?: number;
          start_after?: string;
          next_token?: string;
        };
        Returns: {
          name: string;
          id: string;
          metadata: Json;
          updated_at: string;
        }[];
      };
      operation: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      search: {
        Args: {
          prefix: string;
          bucketname: string;
          limits?: number;
          levels?: number;
          offsets?: number;
          search?: string;
          sortcolumn?: string;
          sortorder?: string;
        };
        Returns: {
          name: string;
          id: string;
          updated_at: string;
          created_at: string;
          last_accessed_at: string;
          metadata: Json;
        }[];
      };
      search_legacy_v1: {
        Args: {
          prefix: string;
          bucketname: string;
          limits?: number;
          levels?: number;
          offsets?: number;
          search?: string;
          sortcolumn?: string;
          sortorder?: string;
        };
        Returns: {
          name: string;
          id: string;
          updated_at: string;
          created_at: string;
          last_accessed_at: string;
          metadata: Json;
        }[];
      };
      search_v1_optimised: {
        Args: {
          prefix: string;
          bucketname: string;
          limits?: number;
          levels?: number;
          offsets?: number;
          search?: string;
          sortcolumn?: string;
          sortorder?: string;
        };
        Returns: {
          name: string;
          id: string;
          updated_at: string;
          created_at: string;
          last_accessed_at: string;
          metadata: Json;
        }[];
      };
      search_v2: {
        Args: {
          prefix: string;
          bucket_name: string;
          limits?: number;
          levels?: number;
          start_after?: string;
        };
        Returns: {
          key: string;
          name: string;
          id: string;
          updated_at: string;
          created_at: string;
          metadata: Json;
        }[];
      };
    };
    Enums: {
      buckettype: 'STANDARD' | 'ANALYTICS';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type PublicSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema['Tables'] & PublicSchema['Views'])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions['schema']]['Tables'] &
        Database[PublicTableNameOrOptions['schema']]['Views'])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions['schema']]['Tables'] &
      Database[PublicTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema['Tables'] & PublicSchema['Views'])
    ? (PublicSchema['Tables'] & PublicSchema['Views'])[PublicTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  PublicTableNameOrOptions extends keyof PublicSchema['Tables'] | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions['schema']]['Tables']
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema['Tables']
    ? PublicSchema['Tables'][PublicTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends keyof PublicSchema['Tables'] | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions['schema']]['Tables']
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema['Tables']
    ? PublicSchema['Tables'][PublicTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  PublicEnumNameOrOptions extends keyof PublicSchema['Enums'] | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions['schema']]['Enums'][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema['Enums']
    ? PublicSchema['Enums'][PublicEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema['CompositeTypes']
    ? PublicSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;
