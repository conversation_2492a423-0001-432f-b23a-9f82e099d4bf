import satori, { type SatoriOptions } from 'satori';
import { html } from 'satori-html';

import { icons as lucideIcons } from '@iconify-json/lucide';
import { getIconData, iconToHTML, iconToSVG, replaceIDs } from '@iconify/utils';

import { CONST } from '@/helpers/constants';
import { fetchFont } from '@/helpers/font';

export async function getCharsImageStr({ icon }: { icon: string }) {
  const ogOptions: SatoriOptions = {
    width: 1200,
    height: 630,
    // debug: true,
    embedFont: true,
    fonts: [
      {
        name: 'Noto Sans Thai',
        data: await fetchFont('Noto+Sans+Thai'),
        weight: 700,
        style: 'normal',
      },
    ],
  };
  const iconData = getIconData(lucideIcons, icon.replace('lucide:', ''));
  if (!iconData) {
    throw new Error(`Icon "${icon}" is missing`);
  }
  const renderData = iconToSVG(iconData, {
    height: '60px',
  });

  const iconSvg = iconToHTML(replaceIDs(renderData.body), renderData.attributes);
  const rows = Array.from({ length: 10 }, () => {
    const randomOpacity = Math.floor(Math.random() * 21) + 20; // Generates a random number between 50 and 100
    return `<div tw="flex opacity-${randomOpacity}">${iconSvg.repeat(20)}</div>`;
  }).join('');

  const markup = html(`<div tw="flex flex-col w-full h-full bg-[#1d1f21] text-[#c9cacc]">
		<div tw="flex flex-col p-2">
			${rows}
		</div>
		<div
			tw="absolute bottom-0 flex items-center justify-between w-full px-10 border-t bg-slate-100/5"
		>
			<svg tw="absolute" width="98" height="104" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" version="1.0">
				<path d="m27.64,0.99c-1.09,0.55 -2.29,1.49 -2.69,2.09c-3.13,4.97 -23.72,42.91 -24.27,44.6c-0.8,2.64 -0.8,4.62 -0.1,7.26c0.45,1.64 2.54,3.98 11.79,13.28c6.17,6.22 11.34,11.19 11.49,11.09c0.1,-0.15 -4.82,-5.37 -10.99,-11.59c-10.99,-11.04 -11.24,-11.34 -11.83,-14.07c-0.5,-2.29 -0.5,-3.18 0.1,-5.22c0.8,-2.64 21.98,-41.57 24.22,-44.46c2.88,-3.73 5.97,-4.18 16.11,-2.49c3.28,0.6 6.32,0.99 6.76,0.9c1.29,-0.25 -12.88,-2.39 -15.86,-2.39c-1.69,0 -3.48,0.35 -4.72,0.99zm25.16,2.24c0.15,0.1 5.07,1.04 10.94,1.99c16.51,2.73 21.43,3.73 22.97,4.62c1.89,1.09 3.13,3.28 4.08,6.86l0.75,2.93l-0.3,-2.49c-0.35,-3.38 -2.09,-6.76 -3.98,-7.86c-0.99,-0.55 -7.41,-1.84 -17.65,-3.58c-16.61,-2.83 -17.21,-2.88 -16.81,-2.49zm44.46,59.22c0,8.3 0.15,8.11 -12.28,14.02c-5.27,2.54 -9.65,4.77 -9.75,5.02c-0.25,0.7 18.85,-8.35 20.24,-9.6c0.65,-0.55 1.49,-1.74 1.84,-2.64c0.75,-1.69 0.85,-6.86 0.25,-10.59c-0.25,-1.54 -0.35,-0.35 -0.3,3.78zm-25.86,24.66c-0.1,0.35 -0.25,3.23 -0.4,6.36c-0.25,7.86 -1.44,9.99 -6.07,10.69c-2.44,0.4 -5.37,-0.55 -10.14,-3.23c-2.04,-1.19 -4.18,-1.99 -5.07,-1.99c-0.9,0 -3.68,0.7 -6.27,1.49c-4.97,1.59 -6.81,1.79 -9.7,0.99c-2.73,-0.8 -4.03,-3.18 -4.43,-8.45l-0.35,-4.23l0.1,4.72c0.15,5.52 0.8,7.11 3.18,8.25c2.39,1.14 7.71,0.7 12.23,-0.99c1.84,-0.7 4.03,-1.29 4.72,-1.29c0.75,0 3.48,1.19 6.07,2.59c4.57,2.59 4.72,2.64 8.75,2.64c3.53,0 4.18,-0.15 5.27,-1.14c1.59,-1.49 1.89,-2.98 2.29,-10.69c0.3,-6.07 0.25,-7.06 -0.2,-5.72z" fill="none" id="svg_1"/>
				<path d="m28.63,1.19c-2.34,0.99 -3.68,2.93 -9.85,14.07c-6.76,12.33 -6.91,12.58 -6.66,12.58c0.15,0 3.23,-5.42 6.86,-11.98c7.86,-14.22 8.06,-14.42 14.07,-14.92l4.03,-0.3l-3.48,-0.05c-1.94,-0.05 -4.18,0.25 -4.97,0.6z" fill="#FFA" fill-opacity="0.01" id="svg_2"/>
				<path d="m45.93,5.37c-3.28,2.98 -4.13,4.72 -4.13,8.55c0,2.54 -0.15,2.98 -0.9,2.98c-1.64,0 -5.77,1.59 -7.01,2.69c-2.14,1.89 -3.08,4.82 -2.83,8.65c0.3,4.43 1.14,6.07 4.67,8.8l2.88,2.24l0.05,2.98c0.05,1.64 0.4,4.03 0.85,5.37c0.45,1.34 0.9,4.33 0.99,6.61c0.1,2.29 0.35,4.87 0.55,5.72l0.35,1.54l-3.33,0.3c-4.08,0.3 -5.72,1.59 -5.97,4.62c-0.05,1.04 -0.35,2.39 -0.55,2.93c-0.25,0.55 -0.45,2.34 -0.45,3.98l0,2.98l-2.83,0.9c-3.58,1.09 -4.87,2.64 -4.87,5.82c0,1.79 0.25,2.59 1.14,3.38c0.65,0.6 1.49,1.09 1.89,1.09c0.4,0 1.49,0.5 2.44,1.14c1.74,1.09 1.94,1.09 19.74,1.09c17.65,0 18,0 19.14,-1.04c0.65,-0.6 1.64,-1.24 2.24,-1.39c1.69,-0.55 4.62,-4.23 4.62,-5.82c0,-1.54 -2.44,-3.93 -3.98,-3.93c-1.99,0 -2.49,-0.55 -2.49,-2.69c0,-3.23 -0.9,-5.62 -3.33,-8.8c-1.79,-2.39 -2.64,-3.08 -4.33,-3.53l-2.09,-0.55l0.15,-4.28c0.15,-2.34 0.55,-5.62 0.94,-7.31c0.4,-1.64 0.7,-4.82 0.7,-7.01c0,-3.88 0.05,-4.08 1.44,-5.07c2.44,-1.74 5.02,-9.55 4.33,-13.13c-0.45,-2.29 -3.98,-5.47 -7.36,-6.66l-2.39,-0.9l0,-3.13c0,-3.38 -1.24,-7.61 -2.98,-10.04c-0.85,-1.19 -1.44,-1.49 -2.88,-1.49c-1.49,0 -2.34,0.45 -4.43,2.39zm6.22,0.7c1.59,3.18 2.19,5.47 2.44,9l0.3,3.93l2.78,1.19c3.58,1.59 6.51,4.43 6.51,6.27c0,1.39 -1.04,5.67 -1.89,7.86c-0.25,0.7 -1.29,1.99 -2.29,2.88l-1.79,1.59l0,5.02c0,3.28 -0.25,5.57 -0.75,6.76c-0.4,0.94 -0.85,3.58 -0.99,5.82c-0.15,2.19 -0.4,4.62 -0.55,5.37c-0.25,1.24 -0.1,1.39 1.89,1.89c3.08,0.85 4.77,1.84 5.12,2.98c0.2,0.55 0.9,1.69 1.54,2.49c0.99,1.14 1.34,2.29 1.64,5.27c0.35,3.98 0.8,4.62 3.03,4.67c1.49,0.05 3.48,1.59 3.48,2.69c0,1.19 -1.84,3.08 -4.03,4.03c-1.04,0.5 -1.94,1.09 -1.94,1.34c0,0.2 -8.25,0.4 -18.35,0.4c-15.66,0 -18.4,-0.1 -18.95,-0.75c-0.35,-0.4 -0.94,-0.75 -1.29,-0.75c-0.75,0 -3.68,-2.59 -3.68,-3.18c0,-0.2 0.75,-1.14 1.64,-2.09c1.29,-1.34 2.14,-1.74 4.33,-2.04l2.73,-0.4l0.05,-3.23c0.1,-5.52 0.8,-9.55 1.79,-10.54c0.7,-0.7 1.69,-0.9 4.67,-0.9l3.83,0l-0.35,-2.39c-0.2,-1.29 -0.5,-4.82 -0.75,-7.81c-0.25,-3.03 -0.7,-6.12 -0.99,-6.86c-0.3,-0.8 -0.55,-2.93 -0.55,-4.77l0,-3.38l-3.03,-2.19c-3.53,-2.54 -4.33,-3.83 -4.72,-7.81c-0.25,-2.34 -0.1,-3.38 0.7,-5.22c1.14,-2.54 2.69,-3.48 7.46,-4.43l2.59,-0.55l0,-3.43c0,-4.03 1.19,-6.81 3.48,-8.2c0.8,-0.45 1.49,-1.04 1.49,-1.24c0,-0.2 0.65,-0.4 1.39,-0.4c0.99,0 1.59,0.35 1.99,1.09z" fill="#FFF" id="svg_3"/>
				<path d="m49.76,5.42c0,0.25 -0.75,0.9 -1.69,1.44c-2.73,1.64 -3.48,3.23 -3.78,8.01l-0.25,4.28l-2.73,0.3c-3.58,0.4 -5.42,1.29 -6.81,3.38c-1.34,1.99 -1.54,6.07 -0.45,8.7c0.65,1.54 3.83,4.53 6.12,5.72c0.94,0.45 1.04,0.9 0.94,3.13c-0.05,1.44 0.2,3.73 0.55,5.12c0.4,1.34 0.9,4.72 1.14,7.46c0.25,2.73 0.6,6.36 0.8,8.06l0.35,3.13l-3.73,0c-2.29,0 -4.03,0.25 -4.57,0.65c-1.09,0.8 -1.79,3.98 -2.14,9.85l-0.25,4.28l-2.49,0.3c-4.28,0.55 -6.27,2.64 -4.67,5.07c0.55,0.85 1.44,1.29 3.03,1.49c1.39,0.25 2.24,0.6 2.24,1.04c0,0.6 2.88,0.7 17.16,0.7c14.02,0 17.16,-0.1 17.16,-0.65c0,-0.4 0.9,-0.94 2.04,-1.29c2.44,-0.75 3.93,-2.14 3.93,-3.78c0,-1.34 -0.85,-1.94 -3.58,-2.44c-2.09,-0.4 -2.14,-0.5 -2.59,-5.62c-0.25,-2.59 -0.6,-3.48 -2.34,-5.82c-1.64,-2.19 -2.54,-2.88 -4.13,-3.33c-3.03,-0.8 -3.53,-1.19 -3.38,-2.49c0.1,-0.65 0.35,-3.33 0.6,-5.92c0.2,-2.59 0.65,-5.62 0.94,-6.71c0.3,-1.09 0.55,-4.03 0.55,-6.51l0,-4.53l1.94,-1.74c1.54,-1.34 2.19,-2.49 2.98,-5.22c1.54,-5.17 1.44,-6.22 -1.04,-8.3c-1.09,-0.99 -3.08,-2.14 -4.33,-2.59c-3.48,-1.29 -3.53,-1.39 -3.53,-5.47c0,-3.63 -0.85,-6.71 -2.44,-9c-0.85,-1.14 -1.54,-1.49 -1.54,-0.7zm1.04,6.12c0.3,1.19 0.45,3.58 0.3,5.42c-0.15,1.79 -0.1,3.48 0.05,3.78c0.2,0.35 1.99,1.19 3.98,1.94c2.24,0.8 4.03,1.84 4.77,2.73c1.14,1.34 1.19,1.54 0.6,3.83c-1.14,4.28 -2.09,5.07 -6.07,5.07c-1.29,0 -2.69,-1.19 -2.69,-2.34c0,-0.6 0.55,-0.7 2.88,-0.55c2.73,0.15 2.83,0.1 2.83,-1.04c0,-1.49 -1.64,-2.09 -5.97,-2.04l-4.13,0c-0.65,0 -1.59,0.2 -2.09,0.5c-1.49,0.8 -3.48,0.6 -4.82,-0.5c-0.7,-0.55 -1.74,-0.99 -2.24,-0.99c-0.94,0 -0.94,0.1 0.25,2.09c1.44,2.49 3.83,3.28 6.66,2.34c1.89,-0.6 2.44,-0.4 1.94,0.8c-0.3,0.9 -3.23,2.24 -4.77,2.24c-1.69,0 -5.87,-3.33 -6.22,-5.02c-0.45,-2.04 0.15,-5.17 1.14,-6.07c1.29,-1.09 3.98,-1.84 6.56,-1.84c3.23,0 3.63,-0.6 3.03,-4.72c-0.6,-4.08 -0.25,-6.02 1.14,-6.96c1.64,-1.19 2.19,-0.9 2.83,1.34zm-0.25,24.07c0.65,0.45 1.94,0.99 2.83,1.14l1.59,0.35l-0.15,5.92c-0.1,3.63 -0.45,6.61 -0.9,7.66c-0.4,0.9 -0.7,3.18 -0.7,5.02c0,1.84 -0.2,4.53 -0.5,5.92c-0.6,3.13 -0.1,4.38 1.89,4.77c3.18,0.65 5.32,1.54 5.47,2.34c0.25,1.34 -2.44,1.54 -12.98,1.09c-9.4,-0.45 -9.55,-0.45 -9.7,-1.59c-0.15,-1.04 0,-1.09 3.08,-1.09c1.79,0 3.83,-0.25 4.57,-0.6l1.29,-0.6l-0.35,-8.25c-0.25,-5.97 -0.6,-9 -1.29,-11.04c-0.94,-2.83 -1.14,-6.07 -0.55,-8.2c0.3,-0.9 2.14,-2.24 4.97,-3.58c0.05,0 0.7,0.3 1.39,0.75z" id="svg_4"/>
				<g fill="#FF0" id="svg_5" stroke-width="0">
				<path d="m31.02,7.06c-0.3,0.35 -2.78,4.67 -5.47,9.6c-15.42,27.8 -19.05,34.46 -19.05,34.96c0,0.8 24.07,24.47 24.32,23.87c0.1,-0.3 -5.12,-5.82 -11.59,-12.28l-11.74,-11.74l2.29,-3.98c1.24,-2.19 2.59,-4.48 2.98,-5.12c0.7,-1.09 0.94,-0.85 9.75,10.44c4.97,6.36 9.05,11.64 9.1,11.74c0.1,0.1 0.8,-0.55 1.64,-1.39c1.34,-1.34 1.84,-1.49 4.48,-1.49l2.98,0l-0.25,-5.62c-0.15,-3.73 -0.55,-6.46 -1.19,-8.2c-0.5,-1.44 -0.94,-3.93 -0.94,-5.52l0,-2.93l-2.69,-1.99c-3.98,-3.03 -4.97,-4.87 -4.92,-9.5c0.1,-6.41 1.79,-8.9 7.16,-10.54l3.43,-1.04l0,-2.44c0,-1.39 0.35,-3.18 0.8,-3.98c0.4,-0.85 0.7,-1.59 0.6,-1.69c-0.1,-0.1 -1.04,-0.3 -2.14,-0.5c-7.21,-1.24 -8.95,-1.39 -9.55,-0.65z" id="svg_6"/>
				<path d="m47.77,11.19c-0.5,0.6 -0.55,2.09 -0.3,5.92l0.3,5.12l-3.58,0.3c-5.32,0.4 -6.61,0.99 -7.31,3.33c-0.75,2.54 0,5.72 1.44,6.12c0.9,0.25 0.9,0.1 -0.3,-1.94c-0.7,-1.19 -1.14,-2.54 -0.99,-2.93c0.4,-1.14 2.09,-0.9 3.48,0.5s2.59,1.54 4.92,0.55c1.09,-0.45 2.98,-0.6 5.62,-0.5c5.67,0.25 7.36,0.75 7.56,2.09c0.3,1.94 -0.8,2.73 -3.48,2.49c-1.79,-0.2 -2.39,-0.1 -2.39,0.4c0,0.75 1.84,0.9 4.38,0.4c1.69,-0.35 2.44,-1.74 2.69,-4.92c0.15,-1.74 0,-2.04 -1.24,-2.64c-0.75,-0.35 -1.34,-0.9 -1.34,-1.19c0,-0.3 -0.15,-0.4 -0.3,-0.25c-0.2,0.15 -0.8,0 -1.39,-0.4c-0.55,-0.4 -1.54,-0.75 -2.19,-0.75c-0.6,0 -1.69,-0.6 -2.34,-1.29c-1.09,-1.19 -1.19,-1.54 -0.9,-5.47c0.2,-2.98 0.1,-4.38 -0.35,-4.92c-0.8,-0.94 -1.19,-0.94 -1.99,0z" id="svg_7"/>
				<path d="m56.33,11.04c0.15,0.35 1.44,0.85 2.93,1.04c9.85,1.44 23.57,3.88 23.87,4.18c0.2,0.25 1.69,10.14 3.38,22.03s3.13,22.28 3.28,23.12l0.25,1.49l-0.9,-1.14c-14.37,-18.5 -23.27,-29.79 -23.52,-29.64c-0.15,0.1 -0.6,1.09 -0.94,2.24c-0.4,1.14 -1.44,2.78 -2.29,3.63c-1.54,1.49 -1.59,1.74 -1.94,6.76c-0.2,2.88 -0.7,6.07 -1.04,7.16c-0.4,1.04 -0.7,3.63 -0.7,5.77l0,3.83l2.19,0.85c1.59,0.6 2.73,1.59 4.28,3.63c2.49,3.33 2.93,4.48 3.38,8.16l0.35,2.78l11.19,-5.27l11.19,-5.32l-0.05,-2.19c0,-1.24 -0.15,-3.03 -0.35,-3.98c-0.15,-0.94 -0.4,-2.73 -0.6,-3.98c-0.15,-1.24 -0.4,-2.83 -0.55,-3.53c-0.1,-0.75 -0.3,-2.19 -0.45,-3.23c-0.15,-1.09 -0.35,-2.39 -0.45,-2.98c-0.1,-0.6 -0.35,-2.24 -0.55,-3.68c-0.2,-1.44 -0.45,-3.08 -0.55,-3.68c-0.1,-0.6 -0.3,-1.94 -0.45,-3.03c-0.15,-1.09 -0.35,-2.44 -0.45,-3.03c-0.1,-0.6 -0.35,-2.24 -0.55,-3.68c-0.2,-1.44 -0.45,-3.08 -0.55,-3.68c-0.1,-0.6 -0.3,-1.94 -0.45,-3.03c-0.15,-1.09 -0.35,-2.44 -0.45,-2.98c-0.15,-0.55 -0.25,-1.44 -0.25,-2.04c-0.05,-1.44 -1.09,-2.69 -2.54,-2.93c-0.7,-0.1 -2.04,-0.3 -2.98,-0.45c-0.94,-0.15 -2.29,-0.4 -2.98,-0.55c-0.65,-0.1 -1.99,-0.35 -2.98,-0.5c-0.99,-0.15 -2.34,-0.35 -2.98,-0.5c-0.65,-0.15 -1.99,-0.35 -2.98,-0.5c-0.99,-0.15 -2.34,-0.4 -2.98,-0.5c-0.7,-0.15 -2.09,-0.35 -3.18,-0.5c-1.04,-0.15 -2.54,-0.35 -3.38,-0.5c-1.04,-0.2 -1.44,-0.1 -1.29,0.35zm-9.05,25.76c-0.7,0.55 -1.54,0.99 -1.84,0.99c-0.94,0 -0.85,5.72 0.15,9c0.45,1.49 0.9,4.23 0.99,6.17c0.1,1.89 0.3,5.77 0.5,8.55l0.35,5.07l-1.44,0.65c-0.8,0.3 -2.83,0.6 -4.53,0.6c-1.74,0.05 -3.18,0.25 -3.23,0.55c-0.15,0.55 6.96,1.09 15.66,1.19c4.43,0.05 5.32,-0.1 5.32,-0.7c0,-0.65 -0.6,-0.94 -1.59,-0.8c-0.2,0 -1.64,-0.45 -3.18,-0.99l-2.78,-1.04l0.3,-3.08c0.2,-1.64 0.5,-5.17 0.7,-7.76c0.2,-2.59 0.6,-5.07 0.85,-5.47c0.25,-0.4 0.6,-3.23 0.75,-6.36l0.25,-5.62l-1.84,-0.3c-1.04,-0.15 -2.14,-0.6 -2.44,-0.94c-0.75,-0.94 -1.44,-0.85 -2.93,0.3z" id="svg_8"/>
				</g>
				<path d="m35.64,91.1c-0.55,2.14 -0.35,5.52 0.35,5.27c0.3,-0.15 2.44,-0.9 4.67,-1.69c4.03,-1.39 4.13,-1.49 4.13,-3.08l0,-1.59l-4.43,0c-4.18,0 -4.43,0.05 -4.72,1.09zm21.08,1.29l0,2.39l3.98,2.14l3.98,2.09l0,-2.19c0,-1.19 0.15,-3.23 0.3,-4.53l0.35,-2.29l-8.6,0l0,2.39z" fill="#0F0" id="svg_9"/>
				<path d="m44.79,91.55c0,1.24 0.15,1.49 0.9,1.29c5.52,-1.59 5.12,-1.59 7.96,0.1c1.44,0.85 2.73,1.54 2.83,1.54c0.15,0 0.25,-0.99 0.25,-2.24l0,-2.24l-11.93,0l0,1.54z" fill="#040" id="svg_10"/>
			</svg>
			<div tw="flex items-center">
				<p tw="ml-20 font-semibold">${CONST.title}</p>
			</div>
			<p>by ${CONST.author}</p>
		</div>
	</div>`);
  const svg = await satori(markup, ogOptions);
  return svg;
}
