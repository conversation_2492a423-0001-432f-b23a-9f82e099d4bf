<script setup lang="ts">
import { trpc } from '@/services/trpc';
import { pinia, useUserStore } from '@/stores/user';

import BaseContentCard from './BaseContentCard.vue';

const userStore = useUserStore(pinia);
const { space, name } = storeToRefs(userStore);

const isLoading = ref(false);

async function addTier() {
  if (!space.value) {
    return;
  }

  isLoading.value = true;
  const tierSet = await trpc.tierSet.create.mutate({
    spaceId: space.value?.id,
    displayName: name.value,
  });
  isLoading.value = false;
  window.location.href = `/tiers/${tierSet!.slug}`;
}
</script>

<template>
  <BaseContentCard @click="addTier" :disabled="isLoading">
    <template #title> + New Tier </template>
  </BaseContentCard>
</template>
