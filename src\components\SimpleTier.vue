<script setup lang="ts">
import { useToast } from 'primevue/usetoast';

import { Icon } from '@iconify/vue';

import type { SimpleTierSetInstance } from '@/server/routers/simpleTier';
import { trpc } from '@/services/trpc';

import BaseContentCard from './BaseContentCard.vue';
import SimpleTierSet from './SimpleTierSet.vue';

const toast = useToast();

const tierInfos = ref<Awaited<ReturnType<typeof trpc.simpleTier.list.query>>['data']>([]);
const currentInstance = ref<SimpleTierSetInstance>();
const isShowingTiers = ref(true);
const isLoadingShowingTiers = ref(false);
const isLoadingShowingTier = ref(false);

async function getTiers() {
  isLoadingShowingTiers.value = true;
  const { data } = await trpc.simpleTier.list.query({});
  tierInfos.value = data;
  isLoadingShowingTiers.value = false;
}

async function showTier(tier: (typeof tierInfos.value)[number]) {
  console.log('showTier:', tier);

  try {
    isLoadingShowingTier.value = true;
    const instance = await trpc.simpleTier.retrieve.query({ id: tier.id });
    currentInstance.value = instance as SimpleTierSetInstance;
    isLoadingShowingTier.value = false;
  } catch {
    toast.add({
      severity: 'error',
      summary: `Can't load ${tier.label}`,
      detail: 'Try again later',
      life: 20000,
    });
  }
}

onMounted(() => {
  getTiers();
});
</script>

<template>
  <PrimeToast />
  <SimpleTierSet :instance="currentInstance" @save="getTiers" @delete="getTiers" />
  <div>
    <div class="flex items-center">
      <div class="text-lg font-bold">My Tiers</div>
      <PrimeButton @click="getTiers" class="bg-transparent!">
        <Icon v-if="isLoadingShowingTiers" icon="mdi:loading" class="animate-spin" />
        <Icon v-else icon="lucide:refresh-cw" />
      </PrimeButton>
      <PrimeButton @click="isShowingTiers = !isShowingTiers" class="bg-transparent!">
        <Icon :icon="isShowingTiers ? 'lucide:chevron-up' : 'lucide:chevron-down'" />
      </PrimeButton>
    </div>
    <div v-if="isShowingTiers" class="flex w-full flex-wrap gap-2">
      <div v-for="tier in tierInfos" :key="tier.id" class="w-96">
        <BaseContentCard>
          <template #title> </template>
          <template #body>
            <div class="flex flex-col items-center justify-between gap-2">
              <div class="p-2">{{ tier.label }}</div>
              <PrimeButton @click="showTier(tier)" :loading="isLoadingShowingTier">
                <Icon v-if="isLoadingShowingTier" icon="mdi:loading" class="animate-spin" />
                <Icon v-else icon="lucide:search" />
                Show
              </PrimeButton>
            </div>
          </template>
        </BaseContentCard>
      </div>
    </div>
  </div>
</template>
