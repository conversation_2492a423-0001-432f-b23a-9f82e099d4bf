import { beforeEach, describe, expect, test } from 'vitest';

import { getServerTrpcCaller } from '@/server/caller';

const { caller } = await getServerTrpcCaller({
  cookies: {},
  request: {},
} as any);

describe('TierItem Routes', () => {
  beforeEach(() => {});

  test('should search order correctly', async () => {
    const results1 = await caller.tierItem.list({ take: 3 });
    expect(results1.data.length).toBeGreaterThan(0);

    const gameName = 'Inscryption';
    const results2 = await caller.tierItem.list({
      search: gameName,
      sets: ['suckz'],
      lanes: ['suckz-s'],
      take: 3,
    });
    expect(results2.data[0].fullTitle?.startsWith(gameName)).toBe(true);
  });

  test('should filters correctly', async () => {
    const results = await caller.tierItem.list({
      sets: ['suckz'],
      take: 3,
    });
    await Promise.all(
      results.data.map(async item => {
        const info = await caller.tierItem.retrieve({
          id: item.id,
        });
        const sets = info!.gameSteam.tierItems.map(other => other.tierSet.slug);
        const lanes = info!.gameSteam.tierItems.map(other => other.tierLane!.slug);
        expect(lanes).toContain('sea-go-2023-12');
        expect(sets).toContain('event');
        expect(sets).toContain('hypeness');
        expect(sets).toContain('suckz');
      }),
    );
  });
});
