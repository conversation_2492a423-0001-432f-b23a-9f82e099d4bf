import type { APIContext, GetStaticPathsResult } from 'astro';
import { sql } from 'kysely';
import { jsonArrayFrom, jsonObjectFrom } from 'kysely/helpers/postgres';

import { Resvg } from '@resvg/resvg-js';

import { type KyselyDatabase, kysely } from '@/database/kysely';
import { getSupabaseServerClient } from '@/database/supabase.server';
import { applyComputedTierLane } from '@/database/tierLane';
import { getTierImageStr } from '@/helpers/images/tier-image-utils';
import { logQuery } from '@/helpers/utils';

export const prerender = false;

const query = kysely.selectFrom('tierLane as tl').select(['tl.slug', 'tl.tierSetSlug', 'tl.label']);

export async function getStaticPaths(): Promise<GetStaticPathsResult> {
  const tierLanes = await query.execute();

  return tierLanes.map(tierLane => ({
    params: { tierSetSlug: tierLane.tierSetSlug!, tierLaneSlug: tierLane.slug! },
  }));
}

type Props = {};

type Params = {
  tierSetSlug: string;
  tierLaneSlug: string;
};

export async function GET({ params, request, cookies }: APIContext<Props, Params>) {
  const filePath = `og-image/tiers/${params.tierSetSlug}/${params.tierLaneSlug}.png`;

  const supabase = getSupabaseServerClient(request.headers.get('Cookie'), cookies);
  // const {
  //   data: { publicUrl },
  // } = supabase.storage.from('images').getPublicUrl(filePath);

  // try {
  //   const responseBefore = await fetch(publicUrl, { method: 'HEAD' });
  //   if (responseBefore.status == 200) {
  //     return redirect(publicUrl);
  //   }
  // } catch {
  //   // Proceed to create image
  // }

  const retrieveQuery = query
    .where('tl.tierSetSlug', '=', params.tierSetSlug)
    .where('tl.slug', '=', params.tierLaneSlug)
    .select(eb =>
      jsonObjectFrom(
        eb.selectFrom('tierSet as ts').whereRef('ts.slug', '=', 'tl.tierSetSlug').select('label'),
      ).as('tierSet'),
    )
    .select(eb =>
      jsonArrayFrom(
        eb
          .selectFrom('tierItem as ti')
          .whereRef('ti.tierLaneSlug', '=', 'tl.slug')
          .innerJoin('gameSteam as gs', 'gs.steamId', 'ti.gameSteamId')
          .where(eb3 =>
            eb3.or([
              eb3('gs.hasImageLibraryPoster', '=', true),
              eb3('gs.hasImageHeroCapsule', '=', true),
            ]),
          )
          .selectAll('ti')
          .select(sql<KyselyDatabase['gameSteam']>`to_json(gs.*)`.as('gameSteam')),
      ).as('tierItems'),
    );
  logQuery(retrieveQuery);
  const result = await retrieveQuery.executeTakeFirst();

  if (!result) {
    return new Response(null, {
      status: 404,
      statusText: 'Not found',
    });
  }

  const tierLane = applyComputedTierLane(result);

  const svg = await getTierImageStr({
    title: tierLane.tierSet!.label ?? '',
    subtitle: tierLane.label ?? '',
    images: tierLane.tierItems!.map(item => item.gameSteam?.imageFinalTall!),
    withText: true,
  });
  const png = new Resvg(svg).render().asPng();

  supabase.storage.from('images').upload(filePath, png);

  return new Response(png, {
    headers: { 'Content-Type': 'image/png' },
  });
}
