<script setup lang="ts">
import * as _ from 'lodash-es';
import Select from 'primevue/select';
import ToggleButton from 'primevue/togglebutton';
import ToggleSwitch from 'primevue/toggleswitch';
import Draggable from 'vuedraggable';
import { z } from 'zod';

import { Icon } from '@iconify/vue';
import { useStorage, useVModels, watchIgnorable } from '@vueuse/core';

import { cn } from '@/helpers/cn';
import type { filterSchema } from '@/server/routers/gameSteam';
import { trpc } from '@/services/trpc';
import { useUserStore } from '@/stores/user';

export type GameFilterType = Partial<z.infer<typeof filterSchema>>;

const props = withDefaults(
  defineProps<{
    tierSets?: Awaited<ReturnType<typeof trpc.tierSet.retrieveMany.query>>;
    tierLanes?: Awaited<ReturnType<typeof trpc.tierLane.retrieveMany.query>>;
    disabled?: boolean;
  }>(),
  {
    tierSets: () => [],
    tierLanes: () => [],
    disabled: false,
  },
);

const emit = defineEmits<(e: 'search', filters: GameFilterType) => void>();

const { user } = storeToRefs(useUserStore());

const state = useStorage<GameFilterType>(
  'demoman-filters',
  {
    filters: [],
    sorts: [],
  },
  localStorage,
  {
    mergeDefaults: true,
  },
);

const { tierSets, tierLanes } = useVModels(props, emit);

const showingAdvanced = ref(false);
const showSaveDialog = ref(false);
const presetName = ref('');
const savedPresets = ref<Awaited<ReturnType<typeof trpc.filterPreset.list.query>>>([]);
const showEditDialog = ref(false);
const editingPreset = ref<{
  id: number;
  name: string;
  public: boolean;
} | null>(null);

const filterChoices = computed(() => [
  {
    label: 'Has',
    items: [
      {
        value: 'HAS:steam_review',
        label: 'Has Steam Review',
      },
      {
        value: 'HAS:steam_player',
        label: 'Has Steam Player',
      },
      {
        value: 'HAS:demo',
        label: 'Has Demo',
      },
      {
        value: 'HAS:review',
        label: 'Has Demoman Review',
      },
    ],
  },
  {
    label: 'Platforms',
    items: [
      {
        value: 'HAS:platform_win',
        label: '🪟 Windows',
      },
      {
        value: 'HAS:platform_mac',
        label: '🍎 MacOS',
      },
      {
        value: 'HAS:platform_linux',
        label: '🐧 Linux',
      },
    ],
  },
  {
    label: 'Play Year',
    items: [2022, 2023, 2024, 2025].map(y => ({
      value: `PLAY_YEAR:${y}`,
      label: `Play year ${y}`,
    })),
  },
  {
    label: 'Tiers',
    items: tierSets.value.map(ts => ({
      value: `SET:${ts.slug}`,
      label: `${ts.label}`,
    })),
  },
  {
    label: 'Tags',
    items: tierLanes.value.map(tl => ({
      value: `LANE:${tl.slug}`,
      label: `${tl.icon} ${tl.label}`,
      mainColor: tl.mainColor,
      textColor: tl.textColor,
    })),
  },
]);

const sortChoices = computed(() => [
  {
    label: 'Game Field',
    items: [
      {
        label: 'Steam Positive Reviews',
        value: 'GAME:reviewPositive',
      },
      {
        label: 'Steam Total Reviews',
        value: 'GAME:reviewTotal',
      },
      {
        label: 'Steam Followers',
        value: 'GAME:followerCount',
      },
      {
        label: 'Steam Peak Players',
        value: 'GAME:peakCount',
      },
      {
        label: 'Steam Latest Online Players',
        value: 'GAME:playingCount',
      },
      {
        label: 'Name',
        value: 'GAME:gameName',
      },
      {
        label: 'Steam ID',
        value: 'GAME:steamId',
      },
      {
        label: 'Release Date',
        value: 'GAME:releaseDate',
      },
      {
        label: 'Add Date',
        value: 'GAME:createdAt',
      },
      {
        label: 'Update Date',
        value: 'GAME:updatedAt',
      },
    ],
  },
  {
    label: 'Review',
    items: [
      {
        label: 'Latest Review',
        value: 'LATEST_REVIEW',
      },
      {
        label: 'Total Score',
        value: 'TOTAL_SCORE',
      },
    ],
  },
]);

const localSelectedFilters = ref<NonNullable<GameFilterType['filters']>>([]);
const localSelectedSorts = ref<NonNullable<GameFilterType['sorts']>>([]);
const localSearchText = ref('');

function onSearch() {
  emit('search', {
    ...state.value,
    filters: (state.value.filters ?? []).filter(f => f.on),
    search: localSearchText.value,
  });
}

function compareArr(arr1: any[], arr2: any[]): boolean {
  if (!arr1 || !arr2 || arr1.length !== arr2.length) {
    return false; // Different lengths, can't be equal
  }

  const sortedArr1 = _.sortBy(arr1, v => JSON.stringify(v));
  const sortedArr2 = _.sortBy(arr2, v => JSON.stringify(v));

  return _.isEqual(sortedArr1, sortedArr2);
}

const { ignoreUpdates } = watchIgnorable(
  state,
  () => {
    onSearch();
  },
  { deep: true },
);

async function loadPresets() {
  savedPresets.value = await trpc.filterPreset.list.query();
}

async function saveCurrentFilters() {
  if (!presetName.value) return;

  await trpc.filterPreset.create.mutate({
    name: presetName.value,
    filters: state.value.filters ?? [],
    sorts: state.value.sorts ?? [],
  });

  showSaveDialog.value = false;
  presetName.value = '';
  await loadPresets();
}

function getFilterIdFromUrl(): string | null {
  const params = new URLSearchParams(window.location.search);
  return params.get('f');
}

function updateUrlFilter(filterId: string | null) {
  const url = new URL(window.location.href);
  if (filterId) {
    url.searchParams.set('f', filterId);
  } else {
    url.searchParams.delete('f');
  }
  window.history.replaceState({}, '', url);
}

async function loadPreset(preset: (typeof savedPresets.value)[0], updateUrl = true) {
  localSelectedFilters.value = _.cloneDeep(preset.filters);
  localSelectedSorts.value = _.cloneDeep(preset.sorts);

  if (updateUrl) {
    updateUrlFilter(preset.id.toString());
  }

  onSearch();
}

async function deletePreset(preset: (typeof savedPresets.value)[0]) {
  await trpc.filterPreset.delete.mutate({ id: preset.id });
  await loadPresets();
}

async function startEdit(preset: (typeof savedPresets.value)[0]) {
  editingPreset.value = {
    id: preset.id,
    name: preset.name,
    public: preset.public,
  };
  showEditDialog.value = true;
}

async function updateCurrentFilter() {
  if (!editingPreset.value) return;

  await trpc.filterPreset.update.mutate({
    id: editingPreset.value.id,
    name: editingPreset.value.name,
    public: editingPreset.value.public,
    filters: state.value.filters ?? [],
    sorts: state.value.sorts ?? [],
  });

  showEditDialog.value = false;
  editingPreset.value = null;
  await loadPresets();
}

function isCurrentPreset(preset: (typeof savedPresets.value)[0]): boolean {
  return (
    compareArr(preset.filters, state.value.filters!) && compareArr(preset.sorts, state.value.sorts!)
  );
}

watch(
  localSelectedFilters,
  vals => {
    const newFilters: typeof state.value.filters = vals!.filter(
      val => val.values && val.values.length > 0,
    );

    if (!compareArr(newFilters, state.value.filters!)) {
      state.value.filters = _.cloneDeep(newFilters);
    } else if (!_.isEqual(newFilters, state.value.filters)) {
      ignoreUpdates(() => {
        state.value.filters = _.cloneDeep(newFilters);
      });
    }
  },
  { deep: true },
);

watch(
  localSelectedSorts,
  vals => {
    state.value.sorts = _.cloneDeep(vals);
  },
  { deep: true },
);

// Add watch for removing invalid filter querystring
watch(
  [() => state.value.filters, () => state.value.sorts],
  ([newFilters, newSorts]) => {
    const filterId = getFilterIdFromUrl();
    if (!filterId) return;

    const preset = savedPresets.value.find(p => p.id.toString() === filterId);
    if (
      preset &&
      (!compareArr(preset.filters, newFilters!) || !compareArr(preset.sorts, newSorts!))
    ) {
      updateUrlFilter(null);
    }
  },
  { deep: true },
);

async function init() {
  await loadPresets();

  const filterId = getFilterIdFromUrl();
  if (filterId) {
    const preset = savedPresets.value.find(p => p.id.toString() === filterId);
    if (preset) {
      await loadPreset(preset, false);
    } else {
      updateUrlFilter(null);
    }
  }

  localSelectedFilters.value = state.value.filters ?? [];
  localSelectedSorts.value = state.value.sorts ?? [];

  onSearch();
}

onMounted(() => {
  init();
});
</script>

<template>
  <div class="flex flex-col gap-2">
    <div class="flex gap-1">
      <div class="relative w-full">
        <Icon icon="lucide:search" class="absolute m-3.5 text-xl" />
        <PrimeInputText
          v-model="localSearchText"
          :disabled="disabled"
          placeholder="Search"
          :class="cn('bg-theme-bg')"
          @keyup.enter="onSearch"
        />
      </div>
      <div>
        <PrimeButton @click="showingAdvanced = !showingAdvanced" :disabled="disabled">
          <Icon
            :icon="showingAdvanced ? 'lucide:filter-x' : 'lucide:filter'"
            :class="cn('m-1.5 text-xl')"
          />
        </PrimeButton>
      </div>
    </div>

    <div class="mb-2 flex flex-col gap-1">
      <!-- Preset List -->
      <!-- My Presets -->
      <template v-if="user">
        <h3 class="text-base font-bold">My Filters</h3>
        <div class="grid grid-cols-6 gap-2">
          <!-- Save New Filter Button -->
          <button
            @click="showSaveDialog = true"
            :disabled="disabled"
            :class="
              cn(
                'flex h-20 w-full items-center justify-center',
                'rounded-lg bg-transparent text-xl font-bold text-gray-500',
                'cursor-pointer',
                'transition-all hover:bg-gray-500 hover:shadow-md',
                'border border-gray-600',
              )
            "
            v-tooltip="'Save Current Filter'"
          >
            <Icon icon="lucide:plus" class="h-8 w-8" />
          </button>
          <div
            v-for="preset in savedPresets.filter(p => p.isOwner)"
            :key="preset.id"
            class="group relative"
          >
            <button
              @click="loadPreset(preset)"
              :class="
                cn(
                  'flex h-20 w-full cursor-pointer items-center justify-center',
                  'rounded-lg text-2xl font-bold transition-colors',
                  'hover:bg-gray-700',
                  {
                    'bg-gray-800': !isCurrentPreset(preset),
                    'bg-blue-800 ring-2 ring-blue-500': isCurrentPreset(preset),
                  },
                )
              "
              v-tooltip="preset.name"
            >
              {{ preset.name.slice(0, 4).toUpperCase() }}
            </button>
            <div
              class="absolute top-1 right-1 flex gap-1 opacity-0 transition-opacity group-hover:opacity-100"
            >
              <button
                @click.stop="startEdit(preset)"
                :class="cn('cursor-pointer rounded bg-yellow-600 p-1 text-xs hover:bg-yellow-500')"
                v-tooltip="'Edit'"
              >
                <Icon icon="lucide:edit" class="h-4 w-4" />
              </button>
              <button
                @click.stop="deletePreset(preset)"
                :class="cn('cursor-pointer rounded bg-red-600 p-1 text-xs hover:bg-red-500')"
                v-tooltip="'Delete'"
              >
                <Icon icon="lucide:trash" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </template>

      <!-- Public Presets -->
      <template v-if="savedPresets.some(p => !p.isOwner)">
        <h3 class="text-lg font-bold">Public Filters</h3>
        <div class="grid grid-cols-6 gap-2">
          <div
            v-for="preset in savedPresets.filter(p => !p.isOwner)"
            :key="preset.id"
            class="group relative"
          >
            <button
              @click="loadPreset(preset)"
              :class="
                cn(
                  'flex h-20 w-full cursor-pointer items-center justify-center',
                  'rounded-lg text-2xl font-bold transition-colors',
                  'hover:bg-slate-700',
                  {
                    'bg-slate-800': !isCurrentPreset(preset),
                    'bg-blue-800 ring-2 ring-blue-500': isCurrentPreset(preset),
                  },
                )
              "
              v-tooltip="preset.name"
            >
              {{ preset.name.slice(0, 4).toUpperCase() }}
            </button>
          </div>
        </div>
      </template>
    </div>

    <template v-if="showingAdvanced">
      <!-- Filters -->
      <Draggable
        v-model="localSelectedFilters"
        group="filters"
        filter=".ignore-drag"
        :preventOnFilter="false"
        :delay="300"
        :delayOnTouchOnly="true"
        :animation="150"
        :forceFallback="true"
        :scroll="true"
        :bubbleScroll="true"
        :disabled="disabled"
        :class="cn('flex flex-col gap-1')"
      >
        <template
          #item="{
            element: filter,
            index: i,
          }: {
            element: NonNullable<GameFilterType['filters']>[number];
            index: number;
          }"
        >
          <div class="flex gap-1">
            <ToggleButton
              v-model="filter.on"
              :class="cn('px-1! py-0.5!')"
              :style="{
                '--p-togglebutton-checked-background': 'var(--p-green-800)',
                '--p-togglebutton-background': 'transparent',
                '--p-togglebutton-checked-color': 'white',
                '--p-togglebutton-color': 'var(--p-red-800)',
                '--p-togglebutton-checked-border-color': 'var(--p-green-800)',
                '--p-togglebutton-border-color': 'transparent',
              }"
            >
              <div v-if="filter.on">
                <Icon icon="lucide:power" v-tooltip="'On'" />
              </div>
              <div v-else>
                <Icon icon="lucide:power-off" v-tooltip="'Off'" />
              </div>
            </ToggleButton>
            <ToggleButton
              v-model="filter.incl"
              :disabled="!filter.on"
              :class="cn('px-1! py-0.5!')"
              :style="{
                '--p-togglebutton-background': 'var(--p-rose-800)',
                '--p-togglebutton-checked-background': 'var(--p-bg)',
              }"
            >
              <div v-if="filter.incl">
                <Icon icon="lucide:locate" v-tooltip="'Include'" />
              </div>
              <div v-else>
                <Icon icon="lucide:locate-off" v-tooltip="'Exclude'" />
              </div>
            </ToggleButton>
            <ToggleButton
              :value="filter.op === 'CONTAIN_ALL'"
              :disabled="!filter.on"
              :class="cn('px-1! py-0.5!')"
              :style="{
                '--p-togglebutton-background': 'var(--p-bg)',
                '--p-togglebutton-checked-background': 'var(--p-yellow-500)',
              }"
              @update:modelValue="
                val => (localSelectedFilters[i].op = val ? 'CONTAIN_ALL' : 'CONTAIN_SOME')
              "
            >
              <div v-if="filter.op === 'CONTAIN_ALL'">
                <Icon icon="lucide:ampersand" v-tooltip="'ALL'" />
              </div>
              <div v-else>
                <Icon icon="lucide:badge-help" v-tooltip="'SOME'" />
              </div>
            </ToggleButton>
            <PrimeMultiSelect
              v-model="localSelectedFilters[i].values"
              :disabled="!filter.on"
              :virtualScrollerOptions="{ itemSize: 44 }"
              :options="filterChoices"
              :placeholder="`Filter #${i + 1}`"
              optionGroupLabel="label"
              optionGroupChildren="items"
              optionLabel="label"
              optionValue="value"
              display="chip"
              :class="cn('w-full text-sm')"
              showClear
              filter
              :style="{
                '--p-multiselect-background': 'var(--p-bg)',
              }"
            >
              <template #optiongroup="{ option }: { option: { label: string; value: string } }">
                <div>
                  {{ option.label }}
                </div>
              </template>
              <template
                #option="{
                  option,
                }: {
                  option: { label: string; value: string; mainColor: string; textColor: string };
                }"
              >
                <div
                  :style="{
                    '--main': option.mainColor || 'var(--color-primary)',
                    '--text': option.textColor,
                  }"
                >
                  <div class="animate-glow-sm font-black text-(--text) shadow-(--main)">
                    {{ option.label }}
                  </div>
                </div>
              </template>
            </PrimeMultiSelect>
            <PrimeButton
              :class="cn('bg-red-500! px-1! py-0.5!')"
              @click="localSelectedFilters.splice(i, 1)"
            >
              <Icon icon="lucide:x" />
            </PrimeButton>
          </div>
        </template>
        <template #footer> </template>
      </Draggable>

      <button
        :class="[
          'rounded-md bg-gray-800 text-base transition-colors hover:bg-gray-700',
          'px-2 py-1',
        ]"
        @click.stop="
          localSelectedFilters.push({ values: [], op: 'CONTAIN_SOME', incl: true, on: true })
        "
      >
        <div class="flex items-center justify-center gap-1">
          <Icon icon="lucide:filter" />
          <div>New Filter</div>
        </div>
      </button>

      <!-- Sorts -->
      <Draggable
        v-model="localSelectedSorts"
        group="sorts"
        filter=".ignore-drag"
        :preventOnFilter="false"
        :delay="300"
        :delayOnTouchOnly="true"
        :animation="150"
        :forceFallback="true"
        :scroll="true"
        :bubbleScroll="true"
        :disabled="disabled"
        :class="cn('flex flex-col gap-1')"
      >
        <template
          #item="{
            element: sort,
            index: i,
          }: {
            element: NonNullable<GameFilterType['sorts']>[number];
            index: number;
          }"
        >
          <div class="flex gap-1">
            <ToggleButton
              :value="sort.order === 1"
              :class="cn('px-1! py-0.5!')"
              :style="{
                '--p-togglebutton-background': 'var(--p-bg)',
                '--p-togglebutton-checked-background': 'var(--p-bg)',
              }"
              @update:modelValue="val => (localSelectedSorts[i].order = val ? 1 : -1)"
            >
              <div v-if="sort.order === 1">
                <Icon icon="lucide:arrow-up" v-tooltip="'ASC'" />
              </div>
              <div v-else>
                <Icon icon="lucide:arrow-down" v-tooltip="'DESC'" />
              </div>
            </ToggleButton>
            <Select
              v-model="localSelectedSorts[i].value"
              :virtualScrollerOptions="{ itemSize: 44 }"
              :options="sortChoices"
              :placeholder="`Sort #${i + 1}`"
              optionGroupLabel="label"
              optionGroupChildren="items"
              optionLabel="label"
              optionValue="value"
              display="chip"
              :class="cn('w-full text-sm')"
              showClear
              filter
              :style="{
                '--p-select-background': 'var(--p-bg)',
              }"
            >
              <template #optiongroup="{ option }: { option: { label: string; value: string } }">
                <div>
                  {{ option.label }}
                </div>
              </template>
              <template
                #option="{
                  option,
                }: {
                  option: { label: string; value: string; mainColor: string; textColor: string };
                }"
              >
                <div
                  :style="{
                    '--main': option.mainColor || 'var(--color-primary)',
                    '--text': option.textColor,
                  }"
                >
                  <div class="animate-glow-sm font-black text-(--text) shadow-(--main)">
                    {{ option.label }}
                  </div>
                </div>
              </template>
            </Select>

            <PrimeButton class="bg-red-500! px-1! py-0.5!" @click="localSelectedSorts.splice(i, 1)">
              <Icon icon="lucide:x" />
            </PrimeButton>
          </div>
        </template>
        <template #footer> </template>
      </Draggable>

      <button
        :class="[
          'rounded-md bg-gray-800 text-base transition-colors hover:bg-gray-700',
          'px-2 py-1',
        ]"
        @click.stop="localSelectedSorts.push({ value: 'GAME:createdAt', order: -1 })"
      >
        <div class="flex items-center justify-center gap-1">
          <Icon icon="lucide:list-plus" />
          <div>New Sort</div>
        </div>
      </button>
      <slot name="append"></slot>
    </template>
  </div>

  <PrimeDialog v-model:visible="showSaveDialog" modal header="Save Filter Preset">
    <div class="flex flex-col gap-4">
      <div class="relative flex flex-col">
        <Icon icon="lucide:plus" class="absolute my-3 ml-3 text-xl" />
        <PrimeInputText v-model="presetName" placeholder="Enter preset name" />
      </div>
    </div>
    <template #footer>
      <PrimeButton @click="showSaveDialog = false" class="p-button-text">Cancel</PrimeButton>
      <PrimeButton @click="saveCurrentFilters" :disabled="!presetName">Save</PrimeButton>
    </template>
  </PrimeDialog>

  <PrimeDialog v-model:visible="showEditDialog" modal header="Update Filter Preset">
    <div class="flex flex-col gap-4">
      <div class="flex flex-col gap-2">
        <label for="preset-name">Preset Name</label>
        <PrimeInputText
          v-if="editingPreset"
          v-model="editingPreset.name"
          placeholder="Enter preset name"
        />
      </div>
      <div class="flex items-center gap-2" v-if="editingPreset">
        <ToggleSwitch v-model="editingPreset.public" />
        <span class="text-sm text-gray-400">
          {{ editingPreset.public ? 'Anyone can see this preset' : 'Only you can see this preset' }}
        </span>
      </div>
    </div>
    <template #footer>
      <PrimeButton @click="showEditDialog = false" class="p-button-text">Cancel</PrimeButton>
      <PrimeButton @click="updateCurrentFilter" :disabled="!editingPreset?.name">Save</PrimeButton>
    </template>
  </PrimeDialog>
</template>
