import { GlobalComponentConstructor } from 'primevue/ts-helpers';

declare module '@vue/runtime-core' {
  interface GlobalComponents {
    PrimeToast: GlobalComponentConstructor<import('primevue/toast').default>;
    PrimeCalendar: GlobalComponentConstructor<import('primevue/calendar').default>;
    PrimeDivider: GlobalComponentConstructor<import('primevue/divider').default>;
    PrimeDropdown: GlobalComponentConstructor<import('primevue/dropdown').default>;
    PrimeInputText: GlobalComponentConstructor<import('primevue/inputtext').default>;
    PrimeTextarea: GlobalComponentConstructor<import('primevue/textarea').default>;
    PrimeSelectButton: GlobalComponentConstructor<import('primevue/selectbutton').default>;
    PrimeMultiSelect: GlobalComponentConstructor<import('primevue/multiselect').default>;
    PrimeImage: GlobalComponentConstructor<import('primevue/image').default>;
    PrimeButton: GlobalComponentConstructor<import('primevue/button').default>;
    PrimeMenu: GlobalComponentConstructor<import('primevue/menu').default>;
    PrimeDialog: GlobalComponentConstructor<import('primevue/dialog').default>;
    PrimeInlineMessage: GlobalComponentConstructor<import('primevue/inlinemessage').default>;
    PrimeFieldset: GlobalComponentConstructor<import('primevue/fieldset').default>;
    PrimeConfirmDialog: GlobalComponentConstructor<import('primevue/ConfirmDialog').default>;
    PrimeCustomColorPicker: GlobalComponentConstructor<
      import('@/components/primevue-custom/CustomColorPicker.vue').default
    >;
  }
}
