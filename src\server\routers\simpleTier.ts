import type { Select<PERSON>ueryBuilder } from 'kysely';
import { z } from 'zod';

import { TRPCError } from '@trpc/server';

import { type KyselyDatabase, type KyselyInstance, kysely } from '@/database/kysely';
import { logQuery } from '@/helpers/utils';

import { createRouter, publicProcedure } from '../trpc';

export const simpleTierItemSchema = z.object({
  id: z.number(),
  label: z.string().default(''),
  imgData: z.string().optional(),
  imgUrl: z.string().optional(),
  urls: z.array(z.string()).default([]).optional(),
  refs: z.array(z.object({ url: z.string() })).optional(),
  tags: z
    .array(
      z.object({
        label: z.string(),
        mainColor: z.string().optional(),
        textColor: z.string().optional(),
      }),
    )
    .optional(),
});

export const simpleTierLaneSchema = z.object({
  id: z.number(),
  label: z.string(),
  mainColor: z.string(),
  textColor: z.string(),
  items: z.array(simpleTierItemSchema),
});

export const simpleTierSetSchema = z.object({
  items: z.array(simpleTierLaneSchema),
});

export const simpleTierSetInstanceSchema = z.object({
  spaceId: z.number().nullable(),
  id: z.number().nullable(),
  label: z.string(),
  lexorank: z.string(),
  jsonObject: simpleTierSetSchema,
  canEdit: z.boolean(),
});

export const filterSchema = z.object({
  search: z.string().optional(),
  ids: z.array(z.number()).default([]),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().min(0).default(100),
});

export type SimpleTierItemObject = z.infer<typeof simpleTierItemSchema>;
export type SimpleTierLaneObject = z.infer<typeof simpleTierLaneSchema>;
export type SimpleTierSetObject = z.infer<typeof simpleTierSetSchema>;
export type SimpleTierSetInstance = z.infer<typeof simpleTierSetInstanceSchema>;

export const getSimpleTierFullQuery = (
  input: Partial<z.infer<typeof filterSchema>>,
  tx: KyselyInstance,
) => {
  const cleanInput = filterSchema.parse(input);
  const { search, ids, skip } = cleanInput;
  const take = Math.min(+cleanInput.take, 1000);
  console.log('getSimpleTierFullQuery', cleanInput);

  let query = tx.selectFrom('simpleTier as st').selectAll();

  if (ids.length) {
    query = query.where('st.id', 'in', ids);
  }

  if (skip) {
    query = query.offset(skip);
  }

  return query;
};

function selectCanEdit(
  query: SelectQueryBuilder<KyselyDatabase & { st: KyselyDatabase['simpleTier'] }, 'st', {}>,
  userId: string | null = null,
) {
  return query.select(eb => [
    eb
      .case()
      .when(
        eb
          .selectFrom(['player as p', 'playersOnSpaces as pos'])
          .whereRef('pos.spaceId', '=', 'st.spaceId')
          .where('p.userId', '=', userId)
          .select(eb.fn.countAll().as('canEdit')),
        '>',
        0,
      )
      .then(true)
      .else(false)
      .end()
      .as('canEdit'),
  ]);
}

export const simpleTierRouter = createRouter({
  // ---------------------------
  list: publicProcedure.input(filterSchema).query(async ({ input, ctx: { user } }) => {
    const { skip, take } = input;

    const [result, count] = await kysely.transaction().execute(async tx => {
      const fullQuery = getSimpleTierFullQuery(input, tx);
      // console.log(JSON.stringify(fullQuery, null, 2));

      const [result, count] = await Promise.all([
        fullQuery.clearSelect().select(['id', 'label']).execute(),
        getSimpleTierFullQuery(input, tx)
          .clearSelect()
          .select(eb => eb.fn.countAll().as('count'))
          .executeTakeFirst()
          .then(r => +(r?.count as any)),
      ]);

      return [result, count];
    });

    const simpleTiers = result;

    const currentPage = Math.floor(skip / take);
    const totalPage = Math.ceil(count / take);

    return {
      data: simpleTiers,
      start: skip,
      end: skip + take,
      total: count,
      size: take,
      currentPage: currentPage,
      lastPage: totalPage,
    };
  }),

  // ---------------------------
  retrieve: publicProcedure
    .input(
      z.object({
        id: z.number().int().positive(),
      }),
    )
    .query(async ({ input: { id }, ctx: { user } }) => {
      const fullQuery = selectCanEdit(
        getSimpleTierFullQuery({ ids: [id] }, kysely),
        user?.id,
      ).select(['id', 'label', 'jsonObject', 'spaceId']);
      const result = await fullQuery.executeTakeFirst();
      return result;
    }),
  // ---------------------------
  retrieveMany: publicProcedure.input(filterSchema).query(async ({ input, ctx: { user } }) => {
    const fullQuery = selectCanEdit(getSimpleTierFullQuery(input, kysely), user?.id);
    const result = await fullQuery.execute();
    return result;
  }),
  // ---------------------------
  upsertMany: publicProcedure
    .input(
      z.object({
        items: z.array(
          z.object({
            spaceId: z.number().int().positive(),
            id: z.number().int().positive().nullable().optional(),
            label: z.string().optional(),
            jsonObject: simpleTierSetSchema.optional(),
            lexorank: z.string().optional(),
          }),
        ),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const result = await kysely.transaction().execute(async tx =>
        input.items.map(({ id, ...data }) => {
          const query = id
            ? tx.updateTable('simpleTier as st').where('st.id', '=', id).set(data).returningAll()
            : tx.insertInto('simpleTier').values(data).returningAll();

          logQuery(query);
          return query.executeTakeFirst();
        }),
      );

      return result;
    }),
  // ---------------------------
  delete: publicProcedure
    .input(
      z.object({
        id: z.number().int().positive(),
      }),
    )
    .mutation(async ({ input: { id }, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const query = kysely.deleteFrom('simpleTier as st').where('st.id', '=', id).returningAll();

      const result = await query.executeTakeFirst();

      return result;
    }),
});

export type SimpleTierRouter = typeof simpleTierRouter;
