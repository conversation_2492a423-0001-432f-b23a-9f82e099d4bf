<script setup lang="ts">
import * as _ from 'lodash-es';
import ConfirmDialog from 'primevue/confirmdialog';
import ConfirmPopup from 'primevue/confirmpopup';
import Textarea from 'primevue/textarea';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import type { SortableEvent, SortableOptions } from 'sortablejs';
import Draggable from 'vuedraggable';

import { Icon } from '@iconify/vue';
import { useVModels } from '@vueuse/core';

import { cn } from '@/helpers/cn';
import type { SimpleTierItemObject, SimpleTierLaneObject } from '@/server/routers/simpleTier';

import SimpleTierItem from './SimpleTierItem.vue';
import TheAddItemSimple, { type FileMeta } from './add-item/TheAddItemSimple.vue';

const props = defineProps({
  tierLane: {
    type: Object as () => SimpleTierLaneObject,
    required: true,
  },
  canAdd: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:tierLane', 'delete:tierLane']);
const { tierLane } = useVModels(props, emit);

const toast = useToast();
const confirm = useConfirm();

const laneItems = computed({
  get() {
    return tierLane.value.items;
  },
  set(val) {
    tierLane.value = {
      ...tierLane.value,
      items: val,
    };
  },
});

const tierItemContainer = useTemplateRef('item-container');

const draggingIndexes = ref<number[]>([]);
const isAddItemModalActive = ref(false);
const isEditingLane = ref(false);
const isLoadingUpdate = ref(false);
const isUploadingNewItem = ref(false);
const originalLane = ref<SimpleTierLaneObject>(tierLane.value);
const newTag = ref<NonNullable<SimpleTierItemObject['tags']>[0]>({} as any);

function resetDefaultNewTag() {
  newTag.value = {
    label: '',
    mainColor: '#C6EFCE',
    textColor: '#000',
  };
}
resetDefaultNewTag();

function parseEventData(e: SortableEvent) {
  const oldIndex = +e.item.dataset['index']!;
  const fromId = +e.item.dataset['itemId']!;

  return {
    oldIndex,
    fromId,
  };
}

function getLogColor() {
  return `background:${tierLane.value.mainColor}; color:${tierLane.value.textColor}; border: 1px solid ${tierLane.value.textColor};`;
}

function onStart(e: SortableEvent) {
  if (e.oldIndex === undefined) return;
  const { oldIndex } = parseEventData(e);
  draggingIndexes.value.push(oldIndex);
  // console.log('%c onStart:', getLogColor(), { caller: tierLane.value.label, e });
}

function onEnd(e: SortableEvent) {
  if (e.oldIndex === undefined) return;
  const { oldIndex } = parseEventData(e);
  const index = draggingIndexes.value.findIndex(i => i === oldIndex);
  draggingIndexes.value.splice(index, 1);
  // console.log('%c onEnd:', getLogColor(), { caller: tierLane.value.label, e });
}

function startEdit() {
  isEditingLane.value = true;
  originalLane.value = _.cloneDeep(tierLane.value);
}

async function applyEdit() {
  isEditingLane.value = false;
}

function cancelEdit() {
  tierLane.value = _.cloneDeep(originalLane.value);
  isEditingLane.value = false;
}

function deleteLane(evt: Event) {
  confirm.require({
    group: `delete-lane-${tierLane.value.id}`,
    target: evt.currentTarget as HTMLElement,
    accept: () => {
      cancelEdit();
      emit('delete:tierLane');
    },
    reject: () => {
      //
    },
  });
}

function addItems(images: FileMeta[]) {
  let runningItemId = new Date().valueOf();
  const newItems = images.map(
    img =>
      ({
        ...img,
        id: ++runningItemId,
        label: '',
      }) as SimpleTierItemObject,
  );
  laneItems.value = [...laneItems.value, ...newItems];
}

function addTagItem(index: number, evt?: MouseEvent) {
  const execute = () => {
    isLoadingUpdate.value = true;
    laneItems.value[index].tags = [...(laneItems.value[index].tags || []), newTag.value];
    resetDefaultNewTag();
    isLoadingUpdate.value = false;
  };

  confirm.require({
    group: `tag-item-${tierLane.value.id}`,
    target: evt?.currentTarget as HTMLElement,
    accept: () => {
      execute();
    },
    reject: () => {
      //
    },
  });
}
function removeTagItem(index: number, tagIndex: number, evt?: MouseEvent) {
  const execute = () => {
    isLoadingUpdate.value = true;
    laneItems.value[index].tags!.splice(tagIndex, 1);
    isLoadingUpdate.value = false;
  };

  confirm.require({
    group: `delete-item-${tierLane.value.id}`,
    target: evt?.currentTarget as HTMLElement,
    accept: () => {
      execute();
    },
    reject: () => {
      //
    },
  });
}

function deleteItem(index: number, evt?: MouseEvent) {
  const execute = () => {
    isLoadingUpdate.value = true;
    laneItems.value = [...laneItems.value.slice(0, index), ...laneItems.value.slice(index + 1)];
    isLoadingUpdate.value = false;
  };

  if (isEditingLane.value) {
    execute();
  } else {
    confirm.require({
      group: `delete-item-${tierLane.value.id}`,
      target: evt?.currentTarget as HTMLElement,
      accept: () => {
        execute();
      },
      reject: () => {
        //
      },
    });
  }
}

const options = computed<SortableOptions>(() => {
  return {
    group: { name: 'games' },
    draggable: '.draggable',
    filter: '.ignore-drag',
    preventOnFilter: false,
    delay: 150,
    delayOnTouchOnly: true,
    animation: 150,
    easing: 'cubic-bezier(1, 0, 0, 1)',
    forceFallback: true,
    scroll: true,
    bubbleScroll: true,
    sort: true,
  };
});
</script>

<template>
  <div
    :class="
      cn(
        'group/lane relative flex w-full',
        'rounded-lg border-2 border-solid border-(--main)',
        isEditingLane ? 'flex-wrap md:flex-nowrap' : '',
      )
    "
    :style="{
      '--main': tierLane.mainColor,
      '--text': tierLane.textColor,
    }"
  >
    <div
      :class="[
        'absolute -inset-px -z-10 h-[101%] w-[101%] opacity-20',
        'transition-all duration-1000',
        'rounded-xl bg-linear-to-r from-(--main) via-[#000] to-(--main) blur-md',
        'group-hover/lane:-inset-1 group-hover/lane:opacity-50 group-hover/lane:blur-lg',
        'group-hover/lane:animate-pulse group-hover/lane:duration-200',
      ]"
    ></div>
    <div
      :class="
        cn(
          'relative flex flex-row',
          'w-[20%] md:w-[10%]',
          'items-center justify-center overflow-hidden bg-(--main) text-center',
          'font-black tracking-wide text-(--text)',
        )
      "
    >
      <!-- Lane Name Showing -->
      <div
        v-if="!isEditingLane"
        :class="
          cn(
            'handle cursor-move items-center justify-center overflow-hidden',
            'max-h-28',
            isEditingLane ? 'w-1/2' : 'w-full',
          )
        "
      >
        <span class="p-1 break-all whitespace-pre-wrap">{{ tierLane.label }}</span>
        <div
          :class="
            cn(
              'flex items-center justify-center',
              'text-sm text-(--text)',
              'opacity-0 transition-opacity group-hover/lane:opacity-70',
            )
          "
        >
          <div>
            {{ tierLane.items.length }}
          </div>
          <div :class="cn('cursor-pointer')" @click.stop="startEdit">
            <Icon icon="lucide:settings" />
          </div>
        </div>
      </div>
      <!-- Lane Name Editing -->
      <div v-else class="flex w-full grow flex-col justify-start gap-1 p-1 text-xs md:w-1/4">
        <div class="relative flex flex-col">
          <Icon icon="lucide:text" class="absolute my-2 mr-2 ml-2 text-xs text-(--text)" />
          <Textarea
            v-model="tierLane.label"
            inputId="lane-label"
            :placeholder="tierLane.label ?? ''"
            :class="
              cn(
                'bg-(--main)! py-1! pl-6! text-xs! text-(--text)!',
                'placeholder:text-(--text)! placeholder:opacity-30',
              )
            "
          />
          <!-- <Icon icon="lucide:code" class="text-xs absolute ml-2 mr-2 my-2 text-(--main)" />
          <PrimeInputText
            v-model="lane.slug"
            inputId="lane-slug"
            :placeholder="lane.slug ?? ''"
            :class="cn(
              'text-xs! pl-6! py-1! bg-(--text)! text-(--main)!',
              'placeholder:text-(--main)! placeholder:opacity-30',
            ]"
          /> -->
        </div>
        <div class="flex items-center justify-center gap-1 rounded-md bg-slate-500/10 p-0.5">
          <Icon icon="lucide:paint-bucket" class="inline text-lg" />
          <PrimeCustomColorPicker v-model="tierLane.mainColor" />
          <Icon icon="lucide:pencil-line" class="inline text-lg" />
          <PrimeCustomColorPicker v-model="tierLane.textColor" />
        </div>

        <div class="flex justify-center gap-0.5">
          <PrimeButton
            class="text-theme-text w-8/12! justify-center bg-green-600! text-xs!"
            @click="applyEdit"
            :loading="isLoadingUpdate"
          >
            <Icon icon="lucide:save" />
          </PrimeButton>
          <PrimeButton
            class="text-theme-text w-4/12! justify-center bg-gray-600! text-xs!"
            @click="cancelEdit"
          >
            <Icon icon="mdi:cancel" />
          </PrimeButton>
          <PrimeButton
            :class="
              cn(
                'absolute top-0 left-0 m-0.5 rounded-md p-1',
                'cursor-pointer bg-red-900! hover:bg-red-800! hover:opacity-100 hover:ring-2 hover:ring-red-700',
                'hover:animate-wiggle hover:animate-duration-75 hover:animate-infinite',
                'text-theme-text opacity-70',
              )
            "
            @click="deleteLane"
            :loading="isLoadingUpdate"
          >
            <Icon icon="lucide:trash" />
          </PrimeButton>
        </div>
      </div>
    </div>

    <Draggable
      v-model="laneItems"
      ref="item-container"
      item-key="id"
      tag="div"
      v-bind="options"
      :class="cn('flex max-h-64 min-h-[80px] w-full flex-wrap overflow-auto', 'm-0.5 gap-0.5')"
      @start="onStart"
      @end="onEnd"
    >
      <template
        #item="{
          element: item,
          index: i,
        }: {
          element: (typeof laneItems)['value'][number];
          index: number;
        }"
      >
        <div :class="cn('draggable relative w-20 rounded bg-gray-500/5')" :key="i">
          <SimpleTierItem v-model:tier-item="laneItems[i]" :index="i" class="tier-item-card" />
          <!-- Item Actions -->
          <div :class="cn('group/actions')">
            <!-- Tag -->
            <div :class="cn('absolute top-0 left-0', 'flex w-full flex-wrap gap-0.5')">
              <div
                v-for="(tag, j) in item.tags"
                :class="
                  cn(
                    'cursor-pointer rounded-md bg-(--tag-main) text-(--tag-text)',
                    'group-hover/actions:opacity-100 hover:animate-pulse hover:bg-lime-600',
                    'flex h-4 w-fit max-w-4 items-center overflow-hidden text-left',
                    'animate-wiggle',
                    isEditingLane ? 'opacity-100' : 'opacity-70',
                  )
                "
                :style="{
                  '--tag-main': tag.mainColor,
                  '--tag-text': tag.textColor,
                }"
                v-tooltip.top="tag.label"
                @click="evt => removeTagItem(i, j, evt)"
              >
                {{ tag.label }}
              </div>
              <div
                :class="
                  cn(
                    'cursor-pointer rounded-md bg-lime-500',
                    'group-hover/actions:opacity-100 hover:animate-pulse hover:bg-lime-600',
                    isEditingLane ? 'opacity-100' : 'opacity-0',
                  )
                "
                v-tooltip.top="'Add Tag'"
                @click="evt => addTagItem(i, evt)"
              >
                <Icon icon="lucide:tag" class="p-0.5" />
              </div>
            </div>
            <div :class="cn('absolute top-0 right-0', 'flex gap-0.5')">
              <!-- Delete -->
              <div
                :class="
                  cn(
                    'cursor-pointer rounded-md bg-red-500',
                    'group-hover/actions:opacity-100 hover:animate-pulse hover:bg-red-600',
                    isEditingLane ? 'opacity-100' : 'opacity-0',
                  )
                "
                @click="evt => deleteItem(i, evt)"
              >
                <Icon icon="lucide:x" />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer v-if="canAdd">
        <!-- Count -->
        <!-- Add item button -->
        <div
          :class="
            cn(
              'group/card',
              'order-1 m-2 flex max-h-[100px] w-20 cursor-pointer items-center justify-center',
              'rounded-lg outline-2 outline-offset-2 outline-gray-400/80 outline-dashed',
              'hover:my-3 hover:text-lg hover:font-black hover:outline-4 hover:outline-offset-4',
              'hover:animate-jump transition-all',
              'bg-gray-500/5 hover:bg-(--main) hover:text-(--text)',
            )
          "
          @click.stop="isAddItemModalActive = true"
        >
          <Icon
            icon="lucide:plus"
            :class="[
              'group-hover/card:animate-wiggle-more mr-1',
              isUploadingNewItem ? 'animate-spin' : '',
            ]"
          />
          <span>New</span>
        </div>
      </template>
    </Draggable>

    <TheAddItemSimple
      v-if="canAdd"
      v-model:visible="isAddItemModalActive"
      v-model:isUploading="isUploadingNewItem"
      @items-submitted="addItems"
    />

    <ConfirmPopup :group="`delete-lane-${tierLane.id}`">
      <template #container="{ acceptCallback, rejectCallback }">
        <div class="rounded p-4">
          <div>
            Are you sure you want to delete
            <strong class="text-red-400">LANE "{{ tierLane.label }}"</strong>
          </div>
          <div v-if="tierLane.items.length > 0">and "{{ tierLane.items.length }}" items ?</div>
          <div class="mt-4 flex items-center gap-2 text-xs">
            <PrimeButton
              class="bg-red-600! hover:bg-red-500!"
              label="DELETE!"
              @click="acceptCallback"
            />

            <PrimeButton
              class="bg-gray-500! hover:bg-gray-500/50!"
              label="No"
              @click="rejectCallback"
            />
          </div>
        </div>
      </template>
    </ConfirmPopup>

    <ConfirmPopup :group="`delete-item-${tierLane.id}`">
      <template #container="{ acceptCallback, rejectCallback }">
        <div class="rounded p-4">
          <div>Are you sure you want to delete?</div>
          <div class="mt-4 flex items-center gap-2 text-xs">
            <PrimeButton
              class="bg-red-600! hover:bg-red-500!"
              label="DELETE!"
              @click="acceptCallback"
            />

            <PrimeButton
              class="bg-gray-500! hover:bg-gray-500/50!"
              label="No"
              @click="rejectCallback"
            />
          </div>
        </div>
      </template>
    </ConfirmPopup>

    <ConfirmDialog :group="`tag-item-${tierLane.id}`">
      <template #container="{ acceptCallback, rejectCallback }">
        <div class="rounded p-4">
          <div class="flex flex-row gap-1">
            <PrimeCustomColorPicker v-model="newTag.mainColor" />
            <PrimeCustomColorPicker v-model="newTag.textColor" />
            <div class="relative flex grow flex-col">
              <Icon icon="lucide:text" class="absolute my-1 mr-2 ml-2" />
              <PrimeInputText
                v-model="newTag.label"
                inputId="tag-item"
                placeholder="New tag"
                :class="cn('py-1! pl-6! text-xs!')"
              />
            </div>
          </div>
          <div class="mt-4 flex items-center gap-2">
            <PrimeButton
              class="bg-green-600! hover:bg-green-500!"
              label="TAG!"
              :disabled="!newTag.label"
              @click="acceptCallback"
            />

            <PrimeButton
              class="bg-gray-500! hover:bg-gray-500/50!"
              label="Cancel"
              @click="rejectCallback"
            />
          </div>
        </div>
      </template>
    </ConfirmDialog>
  </div>
</template>

<style scss="scss" scoped>
@reference '@/styles/global.css';

.sortable-ghost {
  @apply animate-duration-500 animate-pulse;
}
.sortable-drag {
  @apply animate-fade animate-duration-300 animate-once rounded-full;
}
</style>
