declare namespace Types {
  type RelationWrapper<T> = Partial<T>;

  type GameSteam<T = import('@prisma/client').GameSteam> = Omit<T, 'movies' | 'screenshots'> & {
    movies?: {
      mp4: {
        480: string;
        max: string;
      };
      webm: {
        480: string;
        max: string;
      };
      id: string;
      name: string;
    }[];
    screenshots?: {
      path_full: string;
    }[];

    tierItems?: RelationWrapper<TierItem>[];
  };

  type TierItem<T = import('@prisma/client').TierItem> = T & {
    gameSteam?: RelationWrapper<GameSteam> | null;
    fromLane?: RelationWrapper<TierLane>;
    tierLane?: RelationWrapper<TierLane>;
    tierSet?: RelationWrapper<TierSet>;
  };

  type TierSet<T = import('@prisma/client').TierSet> = T & {
    tierLanes?: RelationWrapper<TierLane>[];
    tierItems?: RelationWrapper<TierItems>[];
    space?: RelationWrapper<Space>;
    canEdit?: boolean;
  };

  type TierLane<T = import('@prisma/client').TierLane> = T & {
    tierSet?: TierSet;
    tierItems?: RelationWrapper<TierItem>[];
  };
  type Space<T = import('@prisma/client').Space> = T & {
    tierSets?: RelationWrapper<TierSet>[];
    playersOnSpaces?: RelationWrapper<PlayersOnSpaces>[];
    canEdit?: boolean;
  };
  type Player<T = import('@prisma/client').Player> = T;
  type PlayersOnSpaces<T = import('@prisma/client').PlayersOnSpaces> = T & {
    player?: RelationWrapper<Player>;
    space?: RelationWrapper<Space>;
  };

  type SiteMeta = {
    title: string;
    description?: string;
    ogImage?: string | undefined;
    publishDateString?: string | undefined;
  };
}
