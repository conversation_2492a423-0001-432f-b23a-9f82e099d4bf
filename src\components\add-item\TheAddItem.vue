<script setup lang="ts">
import type { Selectable } from 'kysely';
import { useToast } from 'primevue/usetoast';
import type { MaybeRef } from 'vue';

import { Icon } from '@iconify/vue';
import { useVModels } from '@vueuse/core';

import type { KyselyDatabase, KyselyEnums } from '@/database/kysely';
import { getLexorankBetween } from '@/helpers/utils';
import { trpc } from '@/services/trpc';
import { pinia, useUserStore } from '@/stores/user';

import AddItemFromLane from './AddItemFromLane.vue';
import AddItemGameSteam from './AddItemGameSteam.vue';
import AddItemNormal from './AddItemNormal.vue';

export type NewItemData = {
  _id: number;
} & (
  | {
      type: 'NORMAL';
      targetUrl: string;
      imageUrl: string;
      reviewTitle: string;
      lexorank: string;
    }
  | {
      type: 'GAME_STEAM';
      targetUrl: string;
      gameSteamId: number;
      reviewTitle?: string;
      lexorank: string;
    }
  | {
      type: 'FROM_LANE';
      fromLaneTierSetSlug: string;
      fromLaneSlug: string;
      reviewTitle?: string;
      lexorank: string;
    }
);

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  lane: {
    type: Object as () => Selectable<KyselyDatabase['tierLane']> & {
      tierItems: Selectable<KyselyDatabase['tierItem']>[];
    },
    required: true,
  },
});
const emit = defineEmits(['update:filters', 'update:searchText', 'added']);

const { visible, lane } = useVModels(props, emit);

const connectedToDb = inject<MaybeRef<boolean>>('connectedToDb', false);

const userStore = useUserStore(pinia);
const { isDemomanSpace } = storeToRefs(userStore);

type ADDER_TYPE = KyselyEnums['TierItemType'];
type ItemTypeChoice = { name: string; value: ADDER_TYPE; icon: string; public: boolean };
const ITEM_TYPE_OPTIONS: ItemTypeChoice[] = [
  {
    name: 'Simple',
    value: 'NORMAL',
    icon: 'mdi:card-text',
    public: true,
  },
  {
    name: 'Steam Game',
    value: 'GAME_STEAM',
    icon: 'mdi:steam-box',
    public: false,
  },
  {
    name: 'Sync with other tier lane',
    value: 'FROM_LANE',
    icon: 'mdi:database-sync',
    public: false,
  },
];

const toast = useToast();

const adderType = ref<ADDER_TYPE>('NORMAL');
const isLoadingSubmit = ref(false);

const newItemList = ref<NewItemData[]>([]);

const itemTypeOptions = computed(() => {
  return ITEM_TYPE_OPTIONS.filter(choice => isDemomanSpace.value || choice.public);
});

async function onSubmitItems() {
  isLoadingSubmit.value = true;
  const payload: Parameters<typeof trpc.tierItem.createMany.mutate>[0]['items'] =
    newItemList.value.map((item, i) => ({
      ...item,
      _id: undefined,
      imageUrl: undefined,
      tierSetSlug: props.lane.tierSetSlug,
      tierLaneSlug: props.lane.slug,
      displayUrls: 'imageUrl' in item ? [item.imageUrl] : undefined,
      lexorank: getLexorankBetween(
        i > 0 ? newItemList.value[i - 1].lexorank : lane.value?.tierItems?.at(-1)?.lexorank,
        null,
      ),
    }));
  try {
    if (unref(connectedToDb)) {
      const createdItems = await trpc.tierItem.createMany.mutate({
        items: payload,
      });
      emit('added', createdItems);
    } else {
      emit('added', payload);
    }

    visible.value = false;
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't add some items",
      detail: 'Maybe the something is broken?',
      life: 20000,
    });
  }
  isLoadingSubmit.value = false;
}

// watch(
//   () => adderType,
//   () => {
//     Object.keys(newItemError).forEach(key => {
//       (newItemError as any)[key] = '';
//     });
//   },
// );
</script>

<template>
  <PrimeDialog v-model:visible="visible" modal header="New Items">
    <div class="flex flex-col gap-6">
      <!-- Add more -->
      <PrimeFieldset legend="Add more" toggleable>
        <div class="flex flex-col gap-6">
          <div v-if="isDemomanSpace">
            <label class="mb-1 block font-bold"> Item Type </label>
            <PrimeSelectButton
              v-model="adderType"
              :options="itemTypeOptions"
              :allowEmpty="false"
              :pt="{
                button: {
                  class: 'p-2! text-sm!',
                },
              }"
              optionLabel="name"
              optionValue="value"
            >
              <template
                #option="{ option: { name, icon } }: { option: ItemTypeChoice; index: number }"
              >
                <div
                  :style="{ '--main': lane.mainColor!, '--text': lane.textColor! }"
                  :class="['flex']"
                >
                  <div class="flex items-center justify-center gap-2">
                    <Icon :icon="icon" class="text-sm" />
                    <div class="rounded-l-md text-white">{{ name }}</div>
                  </div>
                </div>
              </template>
            </PrimeSelectButton>
          </div>

          <!-- Add more: NORMAL -->
          <AddItemNormal
            v-if="adderType === 'NORMAL'"
            v-model:new-item-list="newItemList"
            :lane="lane"
            :isLoading="isLoadingSubmit"
          >
          </AddItemNormal>

          <!-- Add more: STEAM -->
          <AddItemGameSteam
            v-if="adderType === 'GAME_STEAM'"
            v-model:new-item-list="newItemList"
            :lane="lane"
            :isLoading="isLoadingSubmit"
          >
          </AddItemGameSteam>

          <!-- Add more: FROM_LANE -->
          <AddItemFromLane
            v-if="adderType === 'FROM_LANE'"
            v-model:new-item-list="newItemList"
            :lane="lane"
            :isLoading="isLoadingSubmit"
          >
          </AddItemFromLane>
        </div>
      </PrimeFieldset>

      <!-- Added Items -->
      <PrimeFieldset legend="To be added" toggleable>
        <div class="flex flex-col gap-3">
          <div
            v-for="(item, i) in newItemList"
            :key="`${item.type}_${item._id}`"
            class="bg-surface-800 outline-theme-text m-2 rounded-lg p-2 outline outline-offset-1"
          >
            <div class="relative flex max-h-40 items-start gap-1 overflow-hidden text-sm">
              <!-- Preview -->
              <div class="h-full w-fit p-0.5">
                <img
                  class="h-auto max-h-40 w-auto rounded-md"
                  :src="
                    'imageUrl' in item
                      ? item.imageUrl
                      : 'https://fakeimg.pl/300x450/64748b/000/?font=noto&text=:('
                  "
                  alt="New item preview"
                />
              </div>
              <!-- Configs -->
              <div class="flex w-full grow flex-col gap-0.5">
                <template v-if="item.type === 'NORMAL'">
                  <div class="flex-auto">
                    <label for="reviewTitle" class="mb-1 block font-bold"> Title </label>
                    <div class="relative">
                      <Icon icon="lucide:type" class="absolute my-4 mr-2 ml-2 text-sm" />
                      <PrimeInputText
                        v-model="item.reviewTitle"
                        inputId="reviewTitle"
                        placeholder="https://example.com"
                        class="pl-6! text-sm!"
                      />
                    </div>
                  </div>
                  <div class="flex-auto">
                    <label for="targetUrl" class="mb-1 block font-bold"> URL </label>
                    <div class="relative">
                      <Icon icon="lucide:link" class="absolute my-4 mr-2 ml-2 text-sm" />
                      <PrimeInputText
                        v-model="item.targetUrl"
                        inputId="targetUrl"
                        placeholder="https://example.com"
                        class="pl-6! text-sm!"
                      />
                    </div>
                  </div>
                </template>
                <template v-if="item.type === 'GAME_STEAM'">
                  <div class="flex-auto">
                    <label for="targetUrl" class="mb-1 block font-bold"> Steam URL </label>
                    <div class="relative">
                      <Icon icon="lucide:link" class="absolute my-4 mr-2 ml-2 text-sm" />
                      <PrimeInputText
                        v-model="item.targetUrl"
                        inputId="targetUrl"
                        placeholder="https://example.com"
                        class="pl-6! text-sm!"
                      />
                    </div>
                  </div>
                </template>
                <template v-if="item.type === 'FROM_LANE'">
                  <div class="flex-auto">
                    <label for="targetUrl" class="mb-1 block font-bold">
                      Sync Lane: {{ item.reviewTitle }}
                    </label>
                  </div>
                </template>
              </div>

              <!-- Delete Btn -->
              <div
                :class="[
                  'absolute top-0 right-0 m-0.5 rounded-md bg-red-500 p-1 opacity-70',
                  'cursor-pointer hover:bg-red-600 hover:opacity-100 hover:ring-2 hover:ring-red-700',
                  'hover:animate-pulse',
                ]"
              >
                <a class="" @click="newItemList.splice(i, 1)">
                  <Icon icon="lucide:x"></Icon>
                </a>
              </div>
            </div>
          </div>
          <!-- Actions -->
          <div class="flex w-full items-center justify-center gap-1">
            <PrimeButton
              :class="[
                'group bg-theme-primary! my-5',
                'text-lg font-bold text-gray-800! transition-all',
              ]"
              :disabled="!newItemList.length"
              :loading="isLoadingSubmit"
              @click="onSubmitItems"
            >
              <Icon v-if="isLoadingSubmit" icon="lucide:loader-2" :class="['mr-1 animate-spin']" />
              <Icon v-else icon="lucide:plus" :class="['mr-1']" />
              <span class="mx-5">
                Add
                <span class="text-theme-secondary group-hover:text-theme-primary font-black">
                  {{ newItemList.length }}
                </span>
                Items
              </span>
            </PrimeButton>
          </div>
        </div>
      </PrimeFieldset>
    </div>
  </PrimeDialog>
</template>

<style scss="scss" scoped>
@reference '@/styles/global.css';

.sortable-ghost {
  @apply animate-duration-500 animate-pulse;
}
.sortable-drag {
  @apply animate-fade animate-duration-300 animate-once rounded-full;
}
</style>
