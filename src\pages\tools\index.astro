---
import ToolCard from '@/components/ToolCard.vue';
import { TOOLS } from '@/helpers/tools';
import Layout from '@/layouts/Layout.astro';

export const prerender = false;
---

<Layout title={'All tools'} showAuth>
  <main>
    <h1 class="title mb-6">All tools</h1>
    <!-- <div class="flex w-full">
      <ChatBox tierItems={tierItem as Types.TierItem[]} client:load />
    </div> -->
    <div class="flex flex-wrap">
      {
        TOOLS.map(tool => (
          <div class="mx-auto w-1/2 p-2">
            <ToolCard tool={tool} client:visible />
          </div>
        ))
      }
    </div>
  </main>
</Layout>
