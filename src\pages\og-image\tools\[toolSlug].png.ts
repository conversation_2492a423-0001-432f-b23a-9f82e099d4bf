import type { APIContext, GetStaticPathsResult } from 'astro';

import { Resvg } from '@resvg/resvg-js';

import { getSupabaseServerClient } from '@/database/supabase.server';
import { getCharsImageStr } from '@/helpers/images/chars-image-utils';
import { TOOLS } from '@/helpers/tools';

export const prerender = false;

export async function getStaticPaths(): Promise<GetStaticPathsResult> {
  return TOOLS.map(tool => ({
    params: { toolSlug: tool.slug },
  }));
}

type Props = {};

type Params = {
  toolSlug: string;
};

export async function GET({ params, request, cookies, redirect }: APIContext<Props, Params>) {
  const filePath = `og-image/tools/${params.toolSlug}.png`;

  const supabase = getSupabaseServerClient(request.headers.get('Cookie'), cookies);
  const {
    data: { publicUrl },
  } = supabase.storage.from('images').getPublicUrl(filePath);

  try {
    const responseBefore = await fetch(publicUrl, { method: 'HEAD' });
    if (responseBefore.status == 200) {
      return redirect(publicUrl);
    }
  } catch {
    // Proceed to create image
  }

  const tool = TOOLS.find(tool => tool.slug === params.toolSlug);

  if (!tool) {
    return new Response(null, {
      status: 404,
      statusText: 'Not found',
    });
  }

  const svg = await getCharsImageStr({
    icon: tool.icon,
  });
  const png = new Resvg(svg).render().asPng();

  supabase.storage
    .from('images')
    .update(filePath, png)
    .then(({ error }) => {
      if (error) {
        console.error(error);
      }
    });

  return new Response(png, {
    headers: { 'Content-Type': 'image/png' },
  });
}
