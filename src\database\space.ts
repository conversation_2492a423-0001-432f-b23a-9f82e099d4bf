import { type ComputedPropertiesOfTierItem, applyComputedTierItem } from './tierItem';
import { type ComputedPropertiesOfTierSet, applyComputedTierSet } from './tierSet';

export type ComputedPropertiesOfSpace = {
  tierSets?: ComputedPropertiesOfTierSet[];
  tierItems?: ComputedPropertiesOfTierItem[];
};

export function applyComputedSpace<T extends object>(space: T) {
  const applied: T & ComputedPropertiesOfSpace = space;

  if (applied.tierSets) {
    applied.tierSets = applied.tierSets.map(applyComputedTierSet);
  }

  if (applied.tierItems) {
    applied.tierItems = applied.tierItems.map(applyComputedTierItem);
  }

  return applied;
}
