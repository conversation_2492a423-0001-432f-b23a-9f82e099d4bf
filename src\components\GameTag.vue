<script setup lang="ts">
defineProps({
  mainColor: {
    type: String,
    default: '#000',
  },
  textColor: {
    type: String,
    default: '#fff',
  },
  icon: {
    type: String,
    default: '#',
  },
  header: {
    type: Boolean,
    default: false,
  },
  noClick: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <a
    :class="[
      'leading-sm rounded-xs px-2 font-bold',
      { 'py-1 text-sm hover:ring-1 hover:ring-slate-500': header },
      { 'text-xs opacity-50 hover:opacity-100': !header },
      icon ? '' : `before:content-['#']`,
    ]"
    :style="{
      color: textColor,
      backgroundImage: `linear-gradient(to right , ${mainColor}, rgba(255,255,255,0.2))`,
      pointerEvents: noClick ? 'none' : 'unset',
    }"
  >
    <span>{{ icon ?? '' }}</span>
    <slot></slot>
  </a>
</template>
