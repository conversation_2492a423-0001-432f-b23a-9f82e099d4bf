<script setup lang="ts">
import { Icon } from '@iconify/vue';

import type { KyselyEnums } from '@/database/kysely';
import { pinia, useItemStore } from '@/stores/item';

const itemStore = useItemStore(pinia);
const { fShowingItemTypes, fSearchText, hasDemo, hasReview, filters, sortingFields } =
  storeToRefs(itemStore);

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const showingAdvanced = ref(false);

const showingItemTypeChoices: { value: KyselyEnums['TierItemType']; label: string }[] = [
  {
    label: 'Normal',
    value: 'NORMAL',
  },
  {
    label: 'Steam',
    value: 'GAME_STEAM',
  },
];

const hasDemoChoices: { value: true | false | null; label: string }[] = [
  {
    label: 'Any',
    value: null,
  },
  {
    label: 'Has Demo',
    value: true,
  },
  {
    label: 'No Demo',
    value: false,
  },
];

const hasReviewChoices: { value: true | false | null; label: string }[] = [
  {
    label: 'Any',
    value: null,
  },
  {
    label: 'Has Review',
    value: true,
  },
  {
    label: 'No Review',
    value: false,
  },
];

const localSearchText = ref(fSearchText.value);
const entered = ref(false);
function onInput() {
  entered.value = false;
}

function onEnter() {
  if (entered.value) {
    return;
  }
  entered.value = true;
  fSearchText.value = localSearchText.value;
}

async function init() {}

onMounted(() => {
  init();
});
</script>

<template>
  <div class="flex flex-col gap-2">
    <div class="flex gap-1">
      <div class="relative w-full">
        <Icon icon="lucide:search" class="absolute m-3.5 text-xl" />
        <PrimeInputText
          v-model="localSearchText"
          :filled="entered"
          :variant="entered ? 'filled' : 'outlined'"
          :disabled="disabled"
          placeholder="Search"
          @input="onInput"
          @keyup.enter="onEnter"
        />
      </div>
      <div>
        <PrimeButton @click="showingAdvanced = !showingAdvanced" :disabled="disabled">
          <Icon
            :icon="showingAdvanced ? 'lucide:filter-x' : 'lucide:filter'"
            class="m-1.5 text-xl"
          />
        </PrimeButton>
      </div>
    </div>

    <template v-if="showingAdvanced">
      <!-- <div class="flex items-center gap-1">
        <div class="whitespace-nowrap">Showing Items:</div>
        <PrimeMultiSelect
          v-model="fShowingItemTypes"
          :options="showingItemTypeChoices"
          :disabled="disabled"
          optionLabel="label"
          optionValue="value"
          placeholder="Select showing items..."
          display="chip"
          class="w-full"
        />
      </div> -->
      <div class="flex items-center gap-1">
        <div class="whitespace-nowrap">Has Demo:</div>
        <PrimeSelectButton
          v-model="hasDemo"
          :options="hasDemoChoices"
          :disabled="disabled"
          optionLabel="label"
          optionValue="value"
          class="w-full"
        />
      </div>
      <div class="flex items-center gap-1">
        <div class="whitespace-nowrap">Has Review:</div>
        <PrimeSelectButton
          v-model="hasReview"
          :options="hasReviewChoices"
          :disabled="disabled"
          optionLabel="label"
          optionValue="value"
          class="w-full"
        />
      </div>
    </template>
  </div>
</template>
