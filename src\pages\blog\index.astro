---
import ReviewBlogTier from '@/components/ReviewBlogTier.astro';
import Layout from '@/layouts/Layout.astro';
import { getServerTrpcCaller } from '@/server/caller';

export const prerender = true;

const { caller } = await getServerTrpcCaller(Astro, { prerender });

const { tierSets } = await caller.blog.fetchTiers();
---

<Layout title={'Blog'}>
  <main class="flex-col gap-2">
    <h1 class="title mb-6">{`Blog - รีวิวเดโมเกมเยอะแยะ`}</h1>
    {
      tierSets.map(tierSet => (
        <div>
          <ReviewBlogTier tierSet={tierSet} />
        </div>
      ))
    }
  </main>
</Layout>

<style></style>
