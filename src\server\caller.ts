import type { APIContext, AstroGlobal } from 'astro';

import { getCreateContextFunction } from '@/server/context';
import { appRouter } from '@/server/routers';
import { createCallerFactory } from '@/server/trpc';

export async function getServerTrpcCaller(
  astroContext: Pick<AstroGlobal<any, any, any> | APIContext, 'cookies' | 'request'>,
  { prerender = false } = {},
) {
  const astro = prerender ? null : astroContext;
  const createCaller = createCallerFactory(appRouter);
  const createContext = getCreateContextFunction({ cookies: astro?.cookies! });
  const context = await createContext({
    req: astro?.request!,
    resHeaders: astro?.request?.headers!,
  });
  const caller = createCaller(context);
  return { caller, ...context };
}
