export async function fetchFont(font: string): Promise<ArrayBuffer> {
  const API = `https://fonts.googleapis.com/css2?family=${font}`;

  const css = await (
    await fetch(API, {
      headers: {
        // Make sure it returns TTF.
        'User-Agent':
          'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_8; de-at) AppleWebKit/533.21.1 (KHTML, like Gecko) Version/5.0.5 Safari/533.21.1',
      },
    })
  ).text();

  const resource = /src: url\((.+)\) format\('(opentype|truetype)'\)/.exec(css);

  const fontUrl = resource?.length
    ? resource[1]!
    : 'https://fonts.cdnfonts.com/s/93128/NotoSansThaiLooped-Medium.woff';

  const res = await fetch(fontUrl);

  return res.arrayBuffer();
}
