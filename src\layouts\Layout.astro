---
import BaseHead from '@/components/BaseHead.astro';
import Search from '@/components/Search.astro';
import TheFooter from '@/components/TheFooter.astro';
import TheHeader from '@/components/TheHeader.vue';
import { CONST } from '@/helpers/constants';

export interface Props extends Types.SiteMeta {
  showNavbar?: boolean;
  showAuth?: boolean;
  showSearch?: boolean;
  isFullWidth?: boolean;
  pagefindIgnore?: boolean;
}

const { themeColorDark, themeColorLight } = CONST;

const {
  title,
  description = CONST.description,
  ogImage,
  showAuth = false,
  showSearch = true,
  publishDateString,
  isFullWidth,
  pagefindIgnore = false,
} = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <BaseHead
      title={title}
      description={description}
      ogImage={ogImage}
      publishDateString={publishDateString}
    />
    <script define:vars={{ themeColorDark, themeColorLight }}>
      const root = document.documentElement;
      const body = document.body;
      const colorThemeMetaTag = document.querySelector("meta[name='theme-color']");

      // get user preference of dark mode, 1st local storage, 2nd browser
      function getThemePreference() {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }

      const isDark = getThemePreference() === 'dark';

      // watch document element class attribute and update user preference when it changes.
      const observer = new MutationObserver(() => {
        const rootIsDark = root.classList.contains('dark');
        // set the meta attribute
        colorThemeMetaTag.setAttribute('content', rootIsDark ? themeColorDark : themeColorLight);
        // store user preference
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('theme', rootIsDark ? 'dark' : 'light');
        }
      });
      observer.observe(root, { attributeFilter: ['class'] });

      // initailse root class attribute
      root?.classList.toggle('dark', isDark);
      body?.classList.toggle('dark', isDark);
    </script>
  </head>
  <body class:list={[isFullWidth ? 'mx-auto max-w-7xl' : 'mx-auto max-w-3xl']}>
    <TheHeader {showAuth} client:only="vue" />
    {showSearch && <Search />}
    <main id="main" class="flex-1" data-pagefind-ignore={pagefindIgnore}>
      <slot />
    </main>
    <TheFooter />

    <style></style>

    <style is:global>
      html {
      }
      :root {
        --astro-gradient: linear-gradient(0deg, #4f39fa, #da62c4);
        --tw-shadow-color: #475569;
      }

      ::-webkit-scrollbar {
        width: 0.1rem;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background-color: #fff;
      }
      ::-webkit-scrollbar-thumb {
        background: #000;
      }
    </style>
  </body>
</html>
