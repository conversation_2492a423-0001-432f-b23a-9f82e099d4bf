<script setup lang="ts">
import GlowBorder from '@/components/inspira-ui/GlowBorder.vue';
import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';

export type UpdatedTierItem = Awaited<ReturnType<typeof trpc.tierItem.upsertFromGameSteam.mutate>>;

const props = defineProps<{
  class?: string;
  game: Awaited<ReturnType<typeof trpc.gameSteam.list.query>>['data'][number];
  tierSets: Awaited<ReturnType<typeof trpc.tierSet.retrieveMany.query>>;
  selected?: boolean;
}>();

const tierLanes = computed(
  () =>
    Object.entries(props.game.rating)
      .map(([tierSetSlug, tierLaneSlug]) =>
        props.tierSets
          .find(ts => ts.slug === tierSetSlug)
          ?.tierLanes.find(tl => tl.slug === tierLaneSlug),
      )
      .filter(tl => !!tl) || [],
);

const emit = defineEmits<(e: 'click') => void>();
</script>

<template>
  <GlowBorder
    :class="
      cn(
        'relative flex h-full w-full flex-col items-center justify-center p-0.5',
        'overflow-hidden text-left',
        { 'animate-glow shadow-xl shadow-(--main)': selected },
        props.class,
      )
    "
    :color="tierLanes?.map(tl => tl.mainColor)"
    :style="{ '--main': tierLanes?.map(tl => tl.mainColor)?.[0] }"
    v-tooltip.top="game.gameName"
  >
    <div
      :class="cn('relative h-full w-full rounded-lg', 'bg-linear-to-r from-theme-bg to-gray-800')"
    >
      <button :class="cn('h-full')" @click="emit('click')">
        <img
          :class="
            cn(
              'absolute left-0 top-0 h-full w-full',
              'rounded-lg object-cover transition-all',
              '[mask-image:linear-gradient(to_right,transparent_20%,#000A_50%)]',
              'hover:[mask-image:linear-gradient(to_right,transparent_50%,#0003_75%)]',
            )
          "
          :src="game.imageFinalWide"
          :alt="game.gameName"
        />
        <div class="font-black text-white/20">
          {{ game.gameName }}
        </div>
      </button>
    </div>
  </GlowBorder>
</template>
