---
import { CONST } from '@/helpers/constants';
import { Tooltips } from 'astro-tooltips';
import '../styles/global.css';

interface Props extends Types.SiteMeta {
  showNavbar?: boolean;
}

const { title, description, ogImage, publishDateString } = Astro.props;

const titleSeparator = '•';
const siteTitle = `${title} ${titleSeparator} ${CONST.title} ${titleSeparator} ${CONST.titleExt}`;
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const socialImageURL = new URL(ogImage ? ogImage : '/og-image/social-card.png', Astro.url).href;
---

<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="generator" content={Astro.generator} />

<!-- Google Search Console -->
<meta name="google-site-verification" content="xn-8mLXfslXLggOknn7MZKnUXlfTXRdkmbGZQzyhkl8" />
<meta name="msvalidate.01" content="B53BDCF12BC31C8E1ADA91138792C0E9" />

<!-- Google Adsense -->
<script
  async
  src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5078011145259602"
  crossorigin="anonymous"></script>

<!-- Font -->
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai+Looped:wght@100;200;300;400;500;600;700;800;900&display=swap"
  rel="stylesheet"
/>

<!-- Icons / Favicon -->
<link rel="icon" href="/favicon.svg" type="image/svg+xml" />
<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
<link rel="manifest" href="/manifest.webmanifest" />
<link rel="canonical" href={canonicalURL} />

<!-- Primary Meta Tags -->
<title>{siteTitle}</title>
<meta name="title" content={siteTitle} />
<meta name="description" content={description} />
<meta name="author" content={CONST.author} />

<!-- Theme Colour -->
<meta name="theme-color" content={CONST.themeColorLight} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={publishDateString ? 'article' : 'website'} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:site_name" content={CONST.title} />
<meta property="og:locale" content={CONST.ogLocale} />
<meta property="og:image" content={socialImageURL} />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
{
  publishDateString && (
    <>
      <meta property="article:author" content={CONST.author} />
      <meta property="article:published_time" content={publishDateString} />
    </>
  )
}

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={canonicalURL} />
<meta property="twitter:title" content={title} />
<meta property="twitter:description" content={description} />
<meta property="twitter:image" content={socialImageURL} />

<!-- Sitemap -->
<link rel="sitemap" href="/sitemap-index.xml" />

<!-- RSS auto-discovery -->
<link rel="alternate" type="application/rss+xml" title={CONST.title} href="/rss.xml" />

<!-- Global Components -->
<Tooltips interactive={false} delay={[15, 200]} />
