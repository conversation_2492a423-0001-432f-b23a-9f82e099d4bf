/*
  Warnings:

  - You are about to drop the `PlayersOnSpaces` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "PlayersOnSpaces" DROP CONSTRAINT "PlayersOnSpaces_playerId_fkey";

-- DropForeignKey
ALTER TABLE "PlayersOnSpaces" DROP CONSTRAINT "PlayersOnSpaces_spaceId_fkey";

-- DropTable
DROP TABLE "PlayersOnSpaces";

-- CreateTable
CREATE TABLE "playersOnSpaces" (
    "playerId" INTEGER NOT NULL,
    "spaceId" INTEGER NOT NULL,
    "assignedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "playersOnSpaces_pkey" PRIMARY KEY ("playerId","spaceId")
);

-- AddF<PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "playersOnSpaces" ADD CONSTRAINT "playersOnSpaces_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "playersOnSpaces" ADD CONSTRAINT "playersOnSpaces_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
