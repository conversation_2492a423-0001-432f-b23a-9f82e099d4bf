<script setup lang="ts">
import Galleria from 'primevue/galleria';
import Tag from 'primevue/tag';

import TierItemLinks from '@/components/TierItemLinks.vue';
import CardContainer from '@/components/inspira-ui/CardContainer.vue';
import CardItem from '@/components/inspira-ui/CardItem.vue';
import GlowBorder from '@/components/inspira-ui/GlowBorder.vue';
import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';

import ParticlesBg from './inspira-ui/ParticlesBg.vue';

export type UpdatedTierItem = Awaited<ReturnType<typeof trpc.tierItem.upsertFromGameSteam.mutate>>;

const props = defineProps<{
  class?: string;
  game: Awaited<ReturnType<typeof trpc.gameSteam.list.query>>['data'][number];
  tierSets: Awaited<ReturnType<typeof trpc.tierSet.retrieveMany.query>>;
}>();

const emit = defineEmits<(e: 'updated', newItem: UpdatedTierItem) => void>();

const galleriaItems = computed(() => {
  const items = [
    ...props.game.movies.map(m => ({ img: m.thumbnail, teaser: m.teaser, video: m.webm.max })),
    ...props.game.screenshots.map(s => ({ img: s.path_full })),
  ];

  if (props.game.hasValidImageWide) {
    items.push({
      img: props.game.imageFinalWide!,
    });
  }

  if (props.game.hasValidImageTall) {
    items.push({
      img: props.game.imageFinalTall!,
    });
  }

  return items;
});

const tierLanes = computed(
  () =>
    Object.entries(props.game.rating)
      .map(([tierSetSlug, tierLaneSlug]) =>
        props.tierSets
          .find(ts => ts.slug === tierSetSlug)
          ?.tierLanes.find(tl => tl.slug === tierLaneSlug),
      )
      .filter(tl => !!tl) || [],
);

const tierLaneSlugToUpdate = ref('');

async function updateTierItem(payload: {
  tierSetSlug: string;
  gameSteamId: number;
  tierLaneSlug: string;
}) {
  tierLaneSlugToUpdate.value = payload.tierLaneSlug;
  const newItem = await trpc.tierItem.upsertFromGameSteam.mutate({
    ...payload,
  });
  emit('updated', newItem);
  tierLaneSlugToUpdate.value = '';
}
</script>

<template>
  <div
    :class="cn('relative h-full w-full', 'md:p-1', props.class)"
    :style="{ '--main': tierLanes?.map(tl => tl.mainColor)?.[0] }"
  >
    <GlowBorder
      :class="cn('flex h-full w-full flex-col items-center justify-center overflow-visible p-1')"
      :borderWidth="4"
      :color="tierLanes?.map(tl => tl.mainColor)"
    >
      <div
        :class="
          cn(
            'bg-theme-bg m-auto',
            'relative h-full w-full rounded-lg',
            'shadow-2xl shadow-slate-500/40 transition-all',
            'hover:shadow-[0px_25px_20px_20px] hover:shadow-slate-500/30',
          )
        "
      >
        <!-- Previews -->
        <!-- Preview Hero Container -->
        <Galleria
          :value="galleriaItems"
          :numVisible="5"
          :pt="{
            root: { class: 'overflow-visible' },
            thumbnailcontent: { class: 'p-1' },
            thumbnailsviewport: { class: 'h-10' },
            thumbnailitem: { class: 'h-fit' },
          }"
        >
          <template #item="{ item }: { item: (typeof galleriaItems.value)[0] }">
            <!-- Preview displaying -->
            <CardContainer :containerClass="cn('z-10')">
              <CardItem :translateZ="100" :translateY="50">
                <div
                  :class="
                    cn(
                      'preview-wrapper group/preview',
                      'flex h-full w-full items-center justify-center',
                      'rounded-lg',
                      'transition-all hover:scale-150',
                    )
                  "
                >
                  <video
                    v-if="'teaser' in item"
                    :src="item.teaser"
                    :class="cn('h-full max-h-[50vh] max-w-none rounded-lg shadow-2xl')"
                    muted
                    loop
                    autoplay
                    __disabled_onmouseover="this.play()"
                    __disabled_onmouseout="this.pause()"
                  >
                    <track label="English" kind="captions" srclang="en" default />
                  </video>
                  <template v-else-if="item.img">
                    <img
                      :src="item.img"
                      :alt="game.gameName"
                      :class="cn('h-full max-h-[50vh] max-w-none rounded-lg shadow-2xl')"
                    />
                  </template>
                </div>
              </CardItem>
            </CardContainer>
          </template>
          <template #thumbnail="{ item }: { item: (typeof galleriaItems.value)[0] }">
            <!-- Preview Thumbnails -->
            <video v-if="'teaser' in item" :src="item.teaser" muted loop autoplay>
              <track label="English" kind="captions" srclang="en" default />
            </video>
            <img v-else :src="item.img" :alt="game.gameName" />
          </template>
        </Galleria>

        <!-- Text -->
        <div :class="cn('relative flex h-fit max-h-1/3 flex-col gap-4 overflow-auto px-8 pt-4')">
          <div :class="cn('text-5xl font-black')">{{ game.gameName }}</div>
          <div :class="cn('grid grid-cols-2')">
            <div :class="cn('flex gap-1')">
              <span :class="cn('font-black')">Release</span>
              <span>{{ game.releaseDateFormatted }}</span>
            </div>
            <div :class="cn('flex gap-1')">
              <span :class="cn('font-black')">Create</span>
              <span>{{ game.createdAtFormatted }}</span>
            </div>
            <div :class="cn('flex gap-1 text-xs')">
              <span :class="cn('font-black')">D</span>
              <span>{{ game.developers.join(', ') }}</span>
            </div>
            <div :class="cn('flex gap-1 text-xs')">
              <span :class="cn('font-black')">P</span>
              <span>{{ game.publishers.join(', ') }}</span>
            </div>
          </div>
          <div :class="cn('text-xs')">
            {{ game.shortDescription }}
          </div>
          <div
            :class="cn('flex w-full flex-wrap gap-0.5 text-xs break-all')"
            v-tooltip="game.gameTags.join('\n')"
          >
            <Tag
              v-for="tag in game.gameTags?.slice(0, 5)"
              :value="tag"
              :key="tag"
              pt:root:class="px-1 mx-0.5 text-xs"
              severity="warn"
            />
          </div>

          <div :class="cn('absolute top-0 right-0 p-1')">
            <TierItemLinks
              :tierItem="{ gameSteam: game, type: 'GAME_STEAM', targetUrl: game.gameUrl }"
            />
          </div>
        </div>

        <!-- Ratings -->
        <!-- <CardContainer :containerClass="cn('z-1 overflow-auto')" :class="cn('w-full')">
          <CardItem :translateZ="10" :class="cn('w-full')"> -->
        <div
          :class="
            cn(
              'relative mb-4 flex h-fit max-h-1/3 flex-col overflow-auto',
              'p-1',
              'md:px-8 md:pt-4',
            )
          "
        >
          <div v-for="set in tierSets" :class="cn('relative flex flex-row gap-1', 'w-full')">
            <div :class="cn('absolute pl-2 font-black')">{{ set.label }}</div>
            <div
              :class="
                cn(
                  'mt-3 flex w-full flex-wrap items-center justify-around rounded-md',
                  'border border-slate-700 hover:border-slate-600',
                  'shadow-xs hover:shadow-lg',
                )
              "
            >
              <button
                v-for="lane in set.tierLanes"
                :class="cn('group/lane', 'relative', 'transition-all hover:scale-125')"
                :style="{
                  '--main': lane.mainColor || 'var(--color-primary)',
                  '--text': lane.textColor,
                }"
                :disabled="!set.canEdit"
                type="button"
                v-tooltip.bottom="{
                  value: lane.label,
                  pt: {
                    text: 'font-black! bg-black/50!',
                  },
                }"
                @click="
                  () =>
                    set.canEdit &&
                    updateTierItem({
                      gameSteamId: game.steamId,
                      tierSetSlug: set.slug,
                      tierLaneSlug: lane.slug === game.rating[set.slug] ? '' : lane.slug,
                    })
                "
              >
                <!-- PULSE -->
                <div
                  :class="
                    cn(
                      'absolute top-1 right-0 left-0 h-12 w-3/4 justify-self-center',
                      'rounded-full shadow-(--main)',
                      'animate-glow group-hover/lane:animate-ping',
                      !set.canEdit ? 'animate-fill-forwards animate-once' : '',
                      lane.slug === game.rating[set.slug] ? 'shadow-xl' : 'shadow-md',
                      lane.slug === tierLaneSlugToUpdate ? 'animate-ping' : '',
                      lane.slug === game.rating[set.slug] || !game.rating[set.slug]
                        ? ''
                        : 'grayscale hover:grayscale-0',
                    )
                  "
                ></div>
                <!-- Text & Icon -->
                <div
                  :class="
                    cn(
                      'my-1 h-12 w-16 text-5xl shadow-(--main) hover:grayscale-0',
                      game.rating[set.slug] ? 'grayscale hover:grayscale-0' : 'grayscale-[50%]',
                      lane.slug === game.rating[set.slug]
                        ? 'animate-glow grayscale-0'
                        : 'group-hover/lane:animate-glow',
                    )
                  "
                >
                  <span v-if="lane.icon">{{ lane.icon }}</span>
                  <span v-else-if="lane.label" :class="cn('text-(--text)')">
                    {{ lane.label.charAt(0) }}
                  </span>
                </div>
              </button>
            </div>
          </div>
        </div>
        <!-- </CardItem>
        </CardContainer> -->
      </div>
    </GlowBorder>
  </div>

  <ParticlesBg
    v-for="(tl, i) in tierLanes.slice(0, 5)"
    :key="tl.slug"
    is-fullscreen
    :quantity="100"
    :staticity="50 - i * 10"
    :circle-size-min="2"
    :circle-size-max="5"
    :color="tl.mainColor"
    :class="cn('fixed top-0 left-0 -z-10')"
  />
  <ParticlesBg
    v-for="(tl, i) in tierLanes.slice(0, 5)"
    :key="tl.slug"
    is-fullscreen
    :text="tl.icon || tl.label.charAt(0)"
    :quantity="10"
    :staticity="50 - i * 10"
    :circle-size-min="2"
    :circle-size-max="5"
    :color="tl.mainColor"
    :class="cn('fixed top-0 left-0 -z-10')"
  />
</template>
