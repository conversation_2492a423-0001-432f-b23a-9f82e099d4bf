<!-- https://tailwindui.com/components#product-ecommerce-components -->
<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { useSessionStorage } from '@vueuse/core';

import { signInWithDiscord, signInWithGoogle, signOut } from '@/services/auth';
import { pinia, useUserStore } from '@/stores/user';

const props = defineProps({});

const sessionMe = useSessionStorage('demoman-me', '');

const userStore = useUserStore(pinia);
const { user, avatarUrl, name, space } = storeToRefs(userStore);
const { fetchUserInfo } = userStore;

const isReady = ref(false);

const menu = ref();

const guestMenuItems = [
  {
    label: 'Sign in with Google',
    icon: 'mdi:google',
    command: () => {
      signInWithGoogle();
    },
  },
  {
    label: 'Sign in with Discord',
    icon: 'mdi:discord',
    command: () => {
      signInWithDiscord();
    },
  },
];

const userMenuItems = computed(() => [
  // {
  //   label: 'My Space',
  //   icon: 'lucide:arrow-up-wide-narrow',
  //   url: `/spaces/${space.value?.slug}`,
  // },
  {
    label: 'Tools',
    icon: 'mdi:desk',
    url: `/tools`,
  },
  {
    label: 'Sign out',
    icon: 'mdi:logout',
    command: async () => {
      await signOut();
      sessionMe.value = '';
    },
  },
]);

function toggleAuthTray(event: Event) {
  menu.value.toggle(event);
}

async function init() {
  isReady.value = false;
  await fetchUserInfo();
  isReady.value = true;
}

onMounted(() => {
  init();
});
</script>

<template>
  <div>
    <div class="flex flex-1 items-center justify-end">
      <PrimeButton
        @click="toggleAuthTray"
        type="button"
        aria-label="Profile"
        aria-haspopup="true"
        aria-controls="overlay_menu"
        class="w-fit"
      >
        <template v-if="!isReady">
          <Icon icon="mdi:run" class="animate-spin"></Icon>
        </template>
        <template v-else-if="user">
          <img
            :src="avatarUrl"
            alt="Profile"
            class="block h-auto w-5 shrink-0 rounded-md"
            referrerpolicy="no-referrer"
          />
          <span class="ml-1 hidden text-sm font-medium md:block">
            {{ name }}
          </span>
        </template>
        <template v-else>
          <span class="text-sm font-medium">Sign in</span>
        </template>
      </PrimeButton>
    </div>
    <PrimeMenu
      ref="menu"
      id="overlay_menu"
      :model="user ? userMenuItems : guestMenuItems"
      :popup="true"
    >
      <template #itemicon="{ item }">
        <Icon :icon="item.icon ?? 'lucide:link'" class="mr-2"></Icon>
      </template>
    </PrimeMenu>
  </div>
</template>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s ease-out;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
