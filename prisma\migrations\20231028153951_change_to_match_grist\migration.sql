/*
  Warnings:

  - You are about to drop the `game_board` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `game_event` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `game_review` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `game_steam` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tier_lane` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tier_set` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "game_board" DROP CONSTRAINT "game_board_game_event_slug_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "game_board" DROP CONSTRAINT "game_board_tier_set_slug_fkey";

-- DropForeignKey
ALTER TABLE "game_review" DROP CONSTRAINT "game_review_game_board_id_fkey";

-- DropForeignKey
ALTER TABLE "game_review" DROP CONSTRAINT "game_review_game_steam_id_fkey";

-- DropForeignKey
ALTER TABLE "game_review" DROP CONSTRAINT "game_review_tier_lane_slug_fkey";

-- DropForeignKey
ALTER TABLE "tier_lane" DROP CONSTRAINT "tier_lane_tier_set_slug_fkey";

-- DropTable
DROP TABLE "game_board";

-- DropTable
DROP TABLE "game_event";

-- DropTable
DROP TABLE "game_review";

-- DropTable
DROP TABLE "game_steam";

-- DropTable
DROP TABLE "tier_lane";

-- DropTable
DROP TABLE "tier_set";

-- CreateTable
CREATE TABLE "gameSteam" (
    "id" SERIAL NOT NULL,
    "gameName" TEXT,
    "gameNameEn" TEXT,
    "gameDescription" TEXT,
    "gameUrl" TEXT,
    "steamId" INTEGER NOT NULL,
    "slug" TEXT,
    "videoTeaser" TEXT,
    "videoTrailer" TEXT,
    "gameTags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "zad" TEXT,
    "recordedVideo" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "nominations" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "awards" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "releaseDate" TIMESTAMP(3),
    "shortDescription" TEXT,
    "detailedDescription" TEXT,
    "aboutTheGame" TEXT,
    "imageLogo" TEXT,
    "imageCapsuleSm" TEXT,
    "imageCapsuleLg" TEXT,
    "imageHeader" TEXT,
    "imageHeroCapsule" TEXT,
    "imageLibraryPoster" TEXT,
    "imageLibraryHero" TEXT,
    "imagePageBgBlur" TEXT,
    "imagePageBgRaw" TEXT,
    "imageBroadcastLeftPanel" TEXT,
    "imageBroadcastRightPanel" TEXT,
    "ogImage" TEXT,
    "reviewTitle" TEXT,
    "reviewContent" TEXT,
    "website" TEXT,
    "publishDate" TIMESTAMP(3),
    "publishers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "developers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "movieId" INTEGER,
    "movies" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "screenshots" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "videos" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "hasAgeCheck" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gameSteam_pkey" PRIMARY KEY ("steamId")
);

-- CreateTable
CREATE TABLE "gameReview" (
    "id" SERIAL NOT NULL,
    "gameSteamId" INTEGER NOT NULL,
    "gameBoardId" INTEGER NOT NULL,
    "tierLaneSlug" TEXT,
    "lexorank" TEXT,
    "note" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gameReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tierLane" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "label" TEXT NOT NULL DEFAULT '',
    "icon" TEXT NOT NULL DEFAULT '',
    "mainColor" TEXT NOT NULL DEFAULT '#000',
    "textColor" TEXT NOT NULL DEFAULT '#fff',
    "description" TEXT NOT NULL DEFAULT '',
    "score" INTEGER NOT NULL DEFAULT 0,
    "lexorank" TEXT,
    "tierSetSlug" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tierLane_pkey" PRIMARY KEY ("slug")
);

-- CreateTable
CREATE TABLE "tierSet" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tierSet_pkey" PRIMARY KEY ("slug")
);

-- CreateTable
CREATE TABLE "gameEvent" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "label" TEXT NOT NULL DEFAULT '',
    "icon" TEXT NOT NULL DEFAULT '',
    "mainColor" TEXT NOT NULL DEFAULT '#000',
    "textColor" TEXT NOT NULL DEFAULT '#fff',
    "description" TEXT NOT NULL DEFAULT '',
    "steamSlug" TEXT NOT NULL DEFAULT '',
    "url" TEXT NOT NULL DEFAULT '',
    "img" TEXT NOT NULL DEFAULT '',
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gameEvent_pkey" PRIMARY KEY ("slug")
);

-- CreateTable
CREATE TABLE "gameBoard" (
    "id" SERIAL NOT NULL,
    "gameEventSlug" TEXT,
    "tierSetSlug" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gameBoard_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "gameSteam_id_key" ON "gameSteam"("id");

-- CreateIndex
CREATE UNIQUE INDEX "gameSteam_steamId_key" ON "gameSteam"("steamId");

-- CreateIndex
CREATE UNIQUE INDEX "gameReview_id_key" ON "gameReview"("id");

-- CreateIndex
CREATE UNIQUE INDEX "gameReview_gameSteamId_gameBoardId_key" ON "gameReview"("gameSteamId", "gameBoardId");

-- CreateIndex
CREATE UNIQUE INDEX "tierLane_id_key" ON "tierLane"("id");

-- CreateIndex
CREATE UNIQUE INDEX "tierLane_slug_key" ON "tierLane"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "tierSet_id_key" ON "tierSet"("id");

-- CreateIndex
CREATE UNIQUE INDEX "tierSet_slug_key" ON "tierSet"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "gameEvent_id_key" ON "gameEvent"("id");

-- CreateIndex
CREATE UNIQUE INDEX "gameEvent_slug_key" ON "gameEvent"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "gameBoard_id_key" ON "gameBoard"("id");

-- CreateIndex
CREATE UNIQUE INDEX "gameBoard_gameEventSlug_tierSetSlug_key" ON "gameBoard"("gameEventSlug", "tierSetSlug");

-- AddForeignKey
ALTER TABLE "gameReview" ADD CONSTRAINT "gameReview_gameSteamId_fkey" FOREIGN KEY ("gameSteamId") REFERENCES "gameSteam"("steamId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gameReview" ADD CONSTRAINT "gameReview_gameBoardId_fkey" FOREIGN KEY ("gameBoardId") REFERENCES "gameBoard"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gameReview" ADD CONSTRAINT "gameReview_tierLaneSlug_fkey" FOREIGN KEY ("tierLaneSlug") REFERENCES "tierLane"("slug") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierLane" ADD CONSTRAINT "tierLane_tierSetSlug_fkey" FOREIGN KEY ("tierSetSlug") REFERENCES "tierSet"("slug") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gameBoard" ADD CONSTRAINT "gameBoard_gameEventSlug_fkey" FOREIGN KEY ("gameEventSlug") REFERENCES "gameEvent"("slug") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gameBoard" ADD CONSTRAINT "gameBoard_tierSetSlug_fkey" FOREIGN KEY ("tierSetSlug") REFERENCES "tierSet"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;
