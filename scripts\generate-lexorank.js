import dotenv from 'dotenv';
import { LexoRank } from 'lexorank';
import pLimit from 'p-limit';

import { PrismaClient } from '@prisma/client';

dotenv.config();

const limit = pLimit(15);

const prisma = new PrismaClient();

generateLexorankList();

export async function generateLexorankList() {
  const arr = await prisma.tierItem.findMany({
    select: {
      id: true,
    },
    orderBy: [
      {
        lexorank: 'asc',
      },
      {
        id: 'asc',
      },
    ],
    where: {
      lexorank: null,
    },
  });
  console.log('[generateLexorankList] Takes:', arr.length);
  const filled = arr;
  arr.forEach((item, i) => {
    if (i === 0) {
      item.lexorank = LexoRank.middle().toString();
    } else {
      const prevStr = filled[i - 1]?.lexorank;
      const nextLexorank = LexoRank.parse(prevStr).genNext();
      const nextStr = nextLexorank.toString();
      item.lexorank = nextStr;
    }
    filled[i] = item;
  });
  const promises = filled.map(item => {
    return limit(() =>
      prisma.tierItem.update({
        where: {
          id: item.id,
        },
        data: {
          lexorank: item.lexorank,
        },
      }),
    );
  });
  await Promise.all(promises);
  console.log('[generateLexorankList] Updated:', filled.length);
}
