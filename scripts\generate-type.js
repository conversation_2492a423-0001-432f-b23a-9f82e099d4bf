// * Get from https://supabase.munverse.me/project/default/api?page=tables-intro instead
import { exec } from 'child_process';
import dotenv from 'dotenv';

dotenv.config();

exec(
  `npx --yes supabase gen types typescript --project-id "%SUPABASE_PROJECT_ID%" --schema public > src/database/supabase.types.ts`,
  (error, stdout, stderr) => {
    if (error) {
      console.log(`error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.log(`stderr: ${stderr}`);
      return;
    }
    console.log(`stdout: ${stdout}`);
    console.log(`DONE !`);
  },
);
