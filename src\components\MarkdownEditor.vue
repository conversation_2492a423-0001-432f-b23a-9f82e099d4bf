<script setup lang="ts">
import { defaultValueCtx, editorViewOptionsCtx, rootCtx } from '@milkdown/core';
import { Crepe } from '@milkdown/crepe';
import '@milkdown/crepe/theme/common/style.css';
import '@milkdown/crepe/theme/frame-dark.css';
import { listener, listenerCtx } from '@milkdown/plugin-listener';
import { Milkdown, useEditor } from '@milkdown/vue';

const props = defineProps<{
  modelValue: string;
  disabled?: boolean;
}>();

const emit = defineEmits<(e: 'update:modelValue', value: string) => void>();

useEditor(root => {
  const crepe = new Crepe({
    root,
  });

  crepe.editor
    .config(ctx => {
      ctx.update(editorViewOptionsCtx, prev => ({
        ...prev,
        editable: () => !props.disabled,
      }));
    })
    .config(ctx => {
      ctx.set(rootCtx, root);

      ctx.set(defaultValueCtx, props.modelValue);

      ctx.get(listenerCtx).markdownUpdated((_, markdown) => {
        emit('update:modelValue', markdown);
      });
    })
    .use(listener);
  return crepe;
});
</script>

<template>
  <Milkdown />
</template>
