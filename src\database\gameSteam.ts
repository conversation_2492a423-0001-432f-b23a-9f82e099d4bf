import { format, isValid } from 'date-fns';
import type { Selectable } from 'kysely';
import * as _ from 'lodash-es';

import type { KyselyDatabase } from './kysely';
import { type ComputedPropertiesOfTierItem, applyComputedTierItem } from './tierItem';
import { type ComputedPropertiesOfTierReview, applyComputedTierReview } from './tierReview';

export type ComputedPropertiesOfGameSteam = {
  movies: {
    mp4: {
      480: string;
      max: string;
    };
    webm: {
      480: string;
      max: string;
    };
    id: string;
    name: string;
    thumbnail?: string;
    teaser?: string;
  }[];
  screenshots: {
    path_full: string;
  }[];
  hasValidImageWide?: boolean;
  hasValidImageTall?: boolean;
  imageFinalWide?: string;
  imageFinalTall?: string;
  imagePageBgBlur?: string;
  ogImage?: string;
  isPlayed?: boolean;
  isReviewed?: boolean;
  isSkipped?: boolean;
  tierItems?: ComputedPropertiesOfTierItem[];
  score?: number;
  reviewPercent?: number;
  createdAtFormatted?: string;
  releaseDateFormatted?: string;
  tierReview?: ComputedPropertiesOfTierReview;
};

export function applyComputedGameSteam<T extends object>(input: T) {
  const applied: Omit<T, 'movies' | 'screenshots'> & ComputedPropertiesOfGameSteam = input as any;
  const game = input as Selectable<KyselyDatabase['gameSteam']>;

  if (applied.tierItems) {
    applied.tierItems = applied.tierItems.map(ti =>
      applyComputedTierItem({
        gameSteam: _.pick(game, 'gameName', 'reviewTitle', 'gameUrl', 'steamId'),
        ...ti,
      }),
    );
  }

  if (applied.tierReview) {
    applied.tierReview = applyComputedTierReview(applied.tierReview);
  }

  const imgCdnHost = 'https://cdn.akamai.steamstatic.com/steam/apps';
  const imageHeader = game.hasImageHeader ? `${imgCdnHost}/${game.steamId}/header.jpg` : '';
  const imageLibraryHero = game.hasImageLibraryHero
    ? `${imgCdnHost}/${game.steamId}/library_hero.jpg`
    : '';
  const imageLibraryPoster = game.hasImageLibraryPoster
    ? `${imgCdnHost}/${game.steamId}/library_600x900.jpg`
    : '';
  const imageHeroCapsule = game.hasImageHeroCapsule
    ? `${imgCdnHost}/${game.steamId}/hero_capsule.jpg`
    : '';
  const imagePageBgBlur = game.hasImagePageBgBlur
    ? `${imgCdnHost}/${game.steamId}/page_bg_generated_v6b.jpg`
    : '';
  // const imagePageBgRaw = game.hasImagePageBgRaw
  //   ? `${imgCdnHost}/${game.steamId}/page_bg_raw.jpg`
  //   : '';
  // const imageLogo = game.hasImageLogo ? `${imgCdnHost}/${game.steamId}/logo.png` : '';
  // const imageCapsuleSm = game.hasImageCapsuleSm
  //   ? `${imgCdnHost}/${game.steamId}/capsule_231x87.jpg`
  //   : '';
  // const imageCapsuleLg = game.hasImageCapsuleLg
  //   ? `${imgCdnHost}/${game.steamId}/capsule_616x353.jpg`
  //   : '';
  // const imageBroadcastLeftPanel = game.hasImageBroadcastLeftPanel
  //   ? `${imgCdnHost}/${game.steamId}/broadcast_left_panel.jpg`
  //   : '';
  // const imageBroadcastRightPanel = game.hasImageBroadcastRightPanel
  //   ? `${imgCdnHost}/${game.steamId}/broadcast_right_panel.jpg`
  //   : '';

  applied.hasValidImageWide = (() => {
    return game.hasImageHeader || game.hasImageLibraryHero;
  })();

  applied.hasValidImageTall = (() => {
    return game.hasImageLibraryPoster || game.hasImageHeroCapsule;
  })();

  applied.imageFinalWide = (() => {
    return (
      imageHeader || imageLibraryHero || 'https://fakeimg.pl/380x420/64748b/000/?font=noto&text=:('
    );
  })();

  applied.imageFinalTall = (() => {
    return (
      imageLibraryPoster ||
      imageHeroCapsule ||
      'https://fakeimg.pl/300x450/64748b/000/?font=noto&text=:('
    );
  })();

  applied.imagePageBgBlur = (() => {
    return imagePageBgBlur || 'https://fakeimg.pl/300x450/64748b/000/?font=noto&text=:(';
  })();

  applied.ogImage = (() => {
    return `/og-image/games/${game.slug}.png`;
  })();

  applied.movies = (() => {
    const movies: ComputedPropertiesOfGameSteam['movies'] = [];
    for (const mv of applied.movies ?? []) {
      if (!mv.mp4?.max || !mv.webm?.max) {
        continue;
      }
      const id = /apps\/(.*)\//.exec(mv.mp4.max)?.[1] ?? mv.id;
      const prefix = mv.webm.max.split('/').slice(0, -1).join('/');
      movies.push({
        ...mv,
        webm: {
          '480': mv?.webm?.['480']?.replace('http://', 'https://'),
          max: mv?.webm?.['max']?.replace('http://', 'https://'),
        },
        mp4: {
          '480': mv?.mp4?.['480']?.replace('http://', 'https://'),
          max: mv?.mp4?.['max']?.replace('http://', 'https://'),
        },
        id,
        thumbnail: `${prefix}/movie.293x165.jpg`,
        teaser: `${prefix}/microtrailer.webm`,
      });
    }

    return movies;
  })();

  const tierItems = (applied.tierItems ?? []) as (Selectable<KyselyDatabase['tierItem']> &
    ComputedPropertiesOfTierItem)[];

  const hypenessItems = tierItems.filter(r => r.tierLaneSlug.startsWith('hypeness'));
  const suckzItems = tierItems.filter(r => r.tierLaneSlug.startsWith('suckz'));

  applied.isPlayed = (() => applied.isPlayed ?? suckzItems.length > 0)();

  applied.isReviewed = (() => {
    return applied.isReviewed ?? !!suckzItems.find(r => r.reviewTitle);
  })();

  applied.isSkipped = (() => {
    return applied.isSkipped ?? !!hypenessItems.find(r => r.tierLaneSlug === 'hypeness-skip');
  })();

  applied.score = (() => {
    return (
      applied.score ??
      tierItems.reduce((acc, cur) => {
        return acc + (cur.score ?? 0);
      }, 0)
    );
  })();

  if ('createdAt' in game) {
    applied.createdAtFormatted = (() => {
      const createdAt = new Date(game.createdAt!);
      return isValid(createdAt) ? format(createdAt, 'yyyy-MM-dd HH:mm') : '';
    })();
  }

  if ('releaseDate' in game) {
    applied.releaseDateFormatted = (() => {
      const releaseDate = new Date(game.releaseDate!);
      return isValid(releaseDate) ? format(releaseDate, 'yyyy-MM-dd') : '';
      // return isValid(releaseDate) ? format(releaseDate, 'dd MMMM yyyy') : '';
    })();
  }

  if (game.reviewTotal! > 0) {
    applied.reviewPercent =
      Math.round(((game.reviewPositive || 0) / game.reviewTotal!) * 10000) / 100;
  }

  return applied;
}
