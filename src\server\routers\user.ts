import { jsonArrayFrom } from 'kysely/helpers/postgres';

import { kysely } from '@/database/kysely';
import { slugifyWithSettings } from '@/helpers/utils';

import { createRouter, publicProcedure } from '../trpc';

export const userRouter = createRouter({
  getMine: publicProcedure.query(async ({ ctx: { user } }) => {
    if (!user) {
      return null;
    }

    return await kysely.transaction().execute(async tx => {
      // Try to create player & space if not exist
      const displayName = user.user_metadata.name ?? user.user_metadata.user_name ?? user.email;
      let player = await tx
        .selectFrom('player')
        .where('userId', '=', user.id)
        .selectAll()
        .select(eb => [
          jsonArrayFrom(
            eb
              .selectFrom('playersOnSpaces as pos')
              .whereRef('pos.playerId', '=', 'player.id')
              .innerJoin('space as sp', 'sp.id', 'pos.spaceId')
              .select(['sp.id', 'sp.label', 'sp.slug']),
          ).as('spaces'),
        ])
        .executeTakeFirst();

      if (!player) {
        player = await tx
          .insertInto('player')
          .values({
            userId: user.id,
            displayName: displayName,
          })
          .returningAll()
          .returning(eb => [
            jsonArrayFrom(
              eb
                .selectFrom('playersOnSpaces as pos')
                .whereRef('pos.playerId', '=', 'player.id')
                .innerJoin('space as sp', 'sp.id', 'pos.spaceId')
                .select(['sp.id', 'sp.label', 'sp.slug']),
            ).as('spaces'),
          ])

          .executeTakeFirst();
      }

      if (!player) {
        return null;
      }

      let space = player.spaces[0];

      if (!space) {
        const newSpace = await tx
          .insertInto('space')
          .values({
            slug: slugifyWithSettings(displayName + '-' + user.id.slice(0, 8)),
            label: `${displayName}'s space`,
          })
          .returning(['id', 'label', 'slug'])
          .executeTakeFirst();

        if (!newSpace) {
          return null;
        }

        await tx
          .insertInto('playersOnSpaces')
          .values({
            playerId: player.id,
            spaceId: newSpace!.id,
          })
          .returningAll()
          .executeTakeFirst();

        space = newSpace;

        console.log('NEW SIGNUP:', user?.email);
      }

      const response = {
        user,
        player,
        space,
      };

      return response;
    });
  }),
  // singupUser: publicProcedure
  //   .input(
  //     z.object({
  //       name: z.string().nullable(),
  //       email: z.string().email(),
  //     }),
  //   )
  //   .mutation(({ input, ctx: { prisma } }) => {
  //     return prisma.user.create({
  //       data: {
  //         email: input.email,
  //         name: input.name ? input.name : undefined,
  //       },
  //     });
  //   }),
});
// export type definition of API
export type UserRouter = typeof userRouter;
