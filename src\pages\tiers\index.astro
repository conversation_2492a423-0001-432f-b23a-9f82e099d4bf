---
import Space from '@/components/Space.vue';
import Layout from '@/layouts/Layout.astro';
import { getServerTrpcCaller } from '@/server/caller';

export const prerender = false;

const { caller } = await getServerTrpcCaller(Astro, { prerender });

const tierSets = await caller.tierSet.retrieveMany({});
---

<Layout title={'All TierSets'} showAuth>
  <main>
    <h1 class="title mb-6">TierSets</h1>
    <Space space={{ tierSets: tierSets }} canEdit={false} client:load />
  </main>
</Layout>

<style></style>
