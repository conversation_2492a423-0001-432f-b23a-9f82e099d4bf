import { defineConfig } from 'astro/config';
import AutoImport from 'unplugin-auto-import/astro';
import IconsResolver from 'unplugin-icons/resolver';
import Icons from 'unplugin-icons/vite';

import sitemap from '@astrojs/sitemap';
import vercel from '@astrojs/vercel';
import vue from '@astrojs/vue';
import tailwindcss from '@tailwindcss/vite';

import { CONST } from './src/helpers/constants';

// https://astro.build/config
export default defineConfig({
  output: 'static',
  adapter: vercel({
    // isr: true,
    maxDuration: 60, // max api 60 secs
    webAnalytics: {
      enabled: true,
    },
  }),

  site: CONST.siteUrl,
  prefetch: true,
  integrations: [
    vue({
      appEntrypoint: '/src/app.ts',
    }),
    AutoImport({
      imports: ['vue', 'pinia'],
      resolvers: [
        IconsResolver({
          prefix: 'Icon',
          componentPrefix: 'icon',
          enabledCollections: ['lucide', 'mdi'],
        }),
      ],
      dts: './src/types/auto-imports.d.ts',
    }),
    sitemap(),
  ],
  vite: {
    plugins: [
      tailwindcss(),
      Icons({
        compiler: 'vue3',
        autoInstall: true,
      }),
      Icons({
        compiler: 'astro',
        autoInstall: true,
      }),
    ],
    server: {
      watch: {
        ignored: [
          '/.github/**/*',
          '/.netlify/**/*',
          '/.vercel/**/*',
          '/.vscode/**/*',
          '/.yarn/**/*',
          '/dist/**/*',
          '/prisma/**/*',
          '/python/**/*',
          '/scripts/**/*',
        ],
      },
    },
    ssr: {
      noExternal: [
        'date-fns',
        'vuedraggable',
        '@uppy/compressor',
        '@uppy/core',
        '@uppy/dashboard',
        '@uppy/drag-drop',
        '@uppy/file-input',
        '@uppy/progress-bar',
        '@uppy/vue',
        '@uppy/xhr-upload',
      ],
      external: ['@primevue/core'],
    },
    optimizeDeps: {
      exclude: ['@resvg/resvg-js'],
    },
  },
});
