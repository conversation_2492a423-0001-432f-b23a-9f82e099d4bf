<script setup lang="ts">
import type { MaybeRef } from 'vue';

import { useElementVisibility } from '@vueuse/core';

import TierItemLinks from '@/components/TierItemLinks.vue';
import type { TierItemForMainPage } from '@/database/tierItem';
import type { TierLaneForMainPage } from '@/database/tierSet';
import { pinia, useItemStore } from '@/stores/item';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
  isMeSelected: {
    type: Boolean,
    default: false,
  },
  isDragging: {
    type: Boolean,
    default: false,
  },
  isCompact: {
    type: Boolean,
    default: false,
  },
  rootContainer: {
    type: Object as () => HTMLElement | null,
    required: false,
    default: null,
  },
  canEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'showing-game-steam', val: Types.GameSteam | null): void;
  (e: 'update:tierItem', val: TierItemForMainPage): void;
}>();

const parent = inject<Ref<TierLaneForMainPage['tierItems']>>(
  'parent',
  ref() as Ref<TierLaneForMainPage['tierItems']>,
);

const tierItem = computed<TierItemForMainPage>({
  get() {
    return parent.value[props.index] as TierItemForMainPage;
  },
  set(val) {
    parent.value[props.index] = val;
  },
});

const connectedToDb = inject<MaybeRef<boolean>>('connectedToDb', false);

const itemStore = useItemStore(pinia);
const { displayingTierItem, canEditDisplayingTierItem } = storeToRefs(itemStore);

const cardRef = ref<HTMLElement>();
const isLoaded = ref(false);
const isVisible = useElementVisibility(cardRef);

// function volumeZero(e: Event) {
//   (e.currentTarget as HTMLVideoElement).volume = 0;
// }

function openTierItemModal() {
  displayingTierItem.value = tierItem.value;
  canEditDisplayingTierItem.value = props.canEdit;
}
</script>

<template>
  <!-- Card -->
  <div
    ref="cardRef"
    :class="[
      'group/itemcard relative h-full w-full rounded',
      'ring-(--main)',
      { 'border-2 border-orange-400': isMeSelected },
      !isCompact ? 'h-[100px] min-h-[100px]' : 'h-[40px] min-h-[40px]',
    ]"
    @click.stop="openTierItemModal"
  >
    <!-- Glowing BG -->
    <!-- <div
      :class="[
        'absolute -z-10 opacity-50 -inset-px',
        'duration-1000 transition-all',
        'bg-(--main) rounded-xl blur-md',
        'group-hover/itemcard:opacity-70 group-hover/itemcard:blur-lg',
        'group-hover/itemcard:duration-200 group-hover/itemcard:animate-pulse',
      ]"
    ></div> -->
    <div
      v-if="isLoaded || isVisible"
      :class="['relative h-full w-full overflow-hidden rounded', { invisible: !isVisible }]"
    >
      <img
        :class="[
          'h-full w-full object-cover hover:scale-105 hover:transition-all',
          canEdit ? 'cursor-grab' : 'cursor-pointer',
        ]"
        :src="tierItem.displayImage"
        :alt="tierItem.fullTitle"
        @load="isLoaded = true"
      />
      <div
        :class="[
          'group/gameinfo',
          'absolute bottom-0 h-3 w-full overflow-hidden',
          'ignore-drag cursor-pointer text-(--text)',
          'transition-height hover:h-full',
        ]"
        :title="tierItem.fullTitle"
      >
        <div
          :class="[
            'absolute left-0 top-0 z-0 h-full w-full bg-(--main)',
            'transition-opacity group-hover/gameinfo:opacity-80',
            tierItem.fullTitle ? 'opacity-100' : 'opacity-30',
          ]"
        ></div>
        <span
          target="_blank"
          :class="[
            'absolute left-0 top-0 z-10 h-full w-full',
            'text-ellipsis text-xxs font-semibold group-hover/gameinfo:text-sm',
          ]"
        >
          {{ tierItem.fullTitle }}
        </span>
        <div
          class="absolute bottom-0 z-20 hidden w-full bg-black text-white group-hover/gameinfo:flex"
        >
          <TierItemLinks :tierItem="tierItem"></TierItemLinks>
        </div>
      </div>
    </div>
  </div>
</template>
