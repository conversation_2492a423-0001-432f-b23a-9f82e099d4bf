<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { useVModels } from '@vueuse/core';

import type { TierLaneForMainPage } from '@/database/tierSet';
import type { APIResponseGetTierLanes } from '@/pages/api/lanes/own';
import { trpc } from '@/services/trpc';

import type { NewItemData } from './TheAddItem.vue';

const props = defineProps({
  newItemList: {
    type: Array as () => NewItemData[],
    required: true,
  },
  lane: {
    type: Object as () => TierLaneForMainPage,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:newItemList']);
const { newItemList } = useVModels(props, emit);

type LaneChoice = { label: string; value: [string, string]; meta: APIResponseGetTierLanes[0] };
const laneChoices = ref<LaneChoice[]>([]);
const addedTierLanes = ref<[string, string][]>([]);

const runningId = ref(1);

async function getTierLanes() {
  const tierLanes = await trpc.tierLane.listMine.query();
  laneChoices.value = tierLanes
    .filter(
      lane =>
        ![
          props.lane.tierSetSlug,
          ...props.lane.tierItems.map(item => item.fromLane?.slug),
        ].includes(lane.slug),
    )
    .map(lane => ({
      label: lane.label ?? '',
      value: [lane.tierSetSlug, lane.slug],
      meta: lane,
    }));
}

function onAdd() {
  const items: (NewItemData & { type: 'FROM_LANE' })[] = addedTierLanes.value.map(ln => {
    const lane = laneChoices.value.find(lc => ln[0] === lc.value[0] && ln[1] === lc.value[1])!;
    return {
      _id: ++runningId.value,
      type: 'FROM_LANE',
      reviewTitle: lane.meta.label ?? '',
      fromLaneSlug: lane.meta.slug,
      fromLaneTierSetSlug: lane.meta.tierSetSlug,
      lexorank: props.lane.tierItems?.at(-1)?.lexorank ?? '',
    };
  });

  newItemList.value.push(...items);
  addedTierLanes.value.length = 0;
}

onMounted(() => {
  getTierLanes();
});
</script>

<template>
  <div class="flex-auto">
    <label for="targetUrl" class="mb-1 block font-bold">
      Sync with these lanes <span class="text-theme-primary">*</span>
      <div class="text-sm font-light">(Will get all items in lane, and keep in sync)</div>
    </label>
    <div class="relative">
      <PrimeMultiSelect
        v-model="addedTierLanes"
        :options="laneChoices"
        optionLabel="label"
        optionValue="value"
        placeholder="Select Filter"
        display="chip"
        class="w-full"
        filter
      >
        <template
          #option="{ option: { value, label, meta: lane } }: { option: LaneChoice; index: number }"
        >
          <div
            :style="{ '--main': lane.mainColor!, '--text': lane.textColor! }"
            :class="[
              'm-0.5 flex w-fit cursor-pointer text-xs ring-white hover:ring-1',
              addedTierLanes.find(ln => ln[0] === lane.tierSetSlug && ln[1] === lane.slug)
                ? 'ring-2'
                : 'opacity-40',
            ]"
          >
            <div class="rounded-l-md bg-black p-1 text-white">{{ lane.tierSet?.label }}:</div>
            <div class="bg-(--main) p-1 text-(--text)">{{ lane.label }}</div>
            <div class="rounded-r-md bg-black p-1 text-white">({{ lane._count.tierItems }})</div>
          </div>
        </template>
        <template #footer>
          <div class="px-3 py-2">
            <b>{{ addedTierLanes ? addedTierLanes.length : 0 }}</b> selected.
          </div>
        </template>
      </PrimeMultiSelect>
    </div>
    <PrimeButton
      :class="['group my-5', 'font-bold transition-all']"
      :disabled="!addedTierLanes.length"
      :loading="isLoading"
      @click="onAdd"
    >
      <Icon icon="lucide:badge-plus" class="mr-1" />
      <span>
        Sync
        <span class="font-black text-theme-secondary group-hover:text-theme-primary">
          {{ addedTierLanes.length }}
        </span>
        Lanes
      </span>
    </PrimeButton>
  </div>
</template>

<style scss="scss" scoped></style>
