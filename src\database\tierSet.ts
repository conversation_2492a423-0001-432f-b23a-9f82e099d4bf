import type { Selectable } from 'kysely';

import type { KyselyDatabase } from './kysely';
import { type ComputedPropertiesOfTierItem, applyComputedTierItem } from './tierItem';
import { type ComputedPropertiesOfTierLane, applyComputedTierLane } from './tierLane';

export type ComputedPropertiesOfTierSet = {
  tierLanes?: ComputedPropertiesOfTierLane[];
  tierItems?: ComputedPropertiesOfTierItem[];
};

export function applyComputedTierSet<T extends object>(tierSet: T) {
  const applied: T & ComputedPropertiesOfTierSet = tierSet;

  if (applied.tierLanes) {
    applied.tierLanes = applied.tierLanes.map(applyComputedTierLane);
  }

  if (applied.tierItems) {
    applied.tierItems = applied.tierItems.map(applyComputedTierItem);
  }

  return applied;
}

export type TierSetForMainPage = NonNullable<
  ReturnType<typeof applyComputedTierSet<Selectable<KyselyDatabase['tierSet']>>>
>;
export type TierLaneForMainPage = NonNullable<TierSetForMainPage['tierLanes']>[number];
