<script setup lang="ts">
import type { Selectable } from 'kysely';

import { Icon } from '@iconify/vue';

import type { KyselyDatabase } from '@/database/kysely';

import BaseContentCard from './BaseContentCard.vue';

const props = defineProps({
  tierLane: {
    type: Object as () => Selectable<KyselyDatabase['tierLane']> & {
      _count: { tierItems: number };
    },
    required: true,
  },
});

const targetUrl = computed(
  () => `/tiers/${props.tierLane.tierSetSlug}/lanes/${props.tierLane.slug}/`,
);

const officialUrl = computed(
  () => `/tiers/hypeness/suckz?sets=${props.tierLane.tierSetSlug}&lanes=${props.tierLane.slug}`,
);

const backgroundUrl = computed(
  () => `/og-image/tiers/${props.tierLane.tierSetSlug}/${props.tierLane.slug}.png`,
);

const itemCount = computed(() => props.tierLane?._count?.tierItems);
</script>

<template>
  <BaseContentCard :url="targetUrl" :bg="backgroundUrl">
    <template #title>
      {{ props.tierLane.label ?? '' }}
    </template>
    <template #body>
      <div class="flex items-center justify-center gap-2 text-sm">
        <div v-if="itemCount" class="flex items-center gap-1">
          <Icon icon="mdi:cards"></Icon>
          {{ Intl.NumberFormat().format(itemCount) }}
        </div>
      </div>
      <!-- <div class="whitespace-pre-wrap">{{ props.tierLane.description ?? '' }}</div> -->
    </template>
    <!-- <template #action>
      <a
        :class="[
          'flex justify-center items-center p-1 gap-1 w-fit',
          'bg-transparent rounded-md outline outline-gray-100 outline-1',
          'hover:bg-white hover:text-black',
        ]"
        :href="officialUrl"
      >
        <Icon icon="mdi:cards-variant" :inline="true"></Icon>
        <span>Official Jud</span>
      </a>
    </template> -->
  </BaseContentCard>
</template>
