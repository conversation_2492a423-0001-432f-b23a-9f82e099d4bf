-- CreateTable
CREATE TABLE "tierSetItemSource" (
    "id" SERIAL NOT NULL,
    "targetTierSetSlug" TEXT NOT NULL,
    "sourceTierLaneId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tierSetItemSource_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tierSetItemSource_id_key" ON "tierSetItemSource"("id");

-- AddForeignKey
ALTER TABLE "tierSetItemSource" ADD CONSTRAINT "tierSetItemSource_targetTierSetSlug_fkey" FOREIGN KEY ("targetTierSetSlug") REFERENCES "tierSet"("slug") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tierSetItemSource" ADD CONSTRAINT "tierSetItemSource_sourceTierLaneId_fkey" FOREIGN KEY ("sourceTierLaneId") REFERENCES "tierLane"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
