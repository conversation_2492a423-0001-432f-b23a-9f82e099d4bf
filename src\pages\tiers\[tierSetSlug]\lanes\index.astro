---
import TierLaneCard from '@/components/TierLaneCard.vue';
import Layout from '@/layouts/Layout.astro';
import IconLucideBookDashed from '~icons/lucide/book-dashed';

import { applyComputedTierLane } from '@/database/tierLane';

import { getServerTrpcCaller } from '@/server/caller';

export const prerender = false;

const { caller } = await getServerTrpcCaller(Astro);

export async function getStaticPaths() {
  const tierSets = await caller.tierSet.retrieveMany({});

  return tierSets.map(tierSet => {
    return {
      params: { tierSetSlug: tierSet.slug },
    };
  });
}

const { tierSetSlug } = Astro.params;

const result = await caller.tierSet.retrieve({ slug: tierSetSlug });

if (!result) {
  return new Response(null, {
    status: 404,
    statusText: 'Not found',
  });
}

const tierLanes = applyComputedTierLane(result.tierLanes);
---

<Layout title="Game List" showAuth>
  <main class="flex flex-wrap">
    {
      tierLanes.length ? (
        <div class="grid w-full gap-2 md:grid-cols-2">
          {tierLanes.map(tierLane => (
            <TierLaneCard tierLane={tierLane} client:visible />
          ))}
        </div>
      ) : (
        <div class="flex h-64 w-full flex-col items-center justify-center gap-5 text-3xl text-theme-text">
          <IconLucideBookDashed />
          <div>This tier is so lonely</div>
          <div class="text-xxs">And it's not your fault.</div>
        </div>
      )
    }
  </main>
</Layout>

<style></style>
