---
import type { Page } from 'astro';
interface Props {
  readonly page: Page;
  readonly baseUrl: string;
}

const { page, baseUrl } = Astro.props;
---

<nav class="flex items-center gap-x-4">
  {
    page.url.prev ? (
      <>
        <a class="ms-auto py-2 sm:hover:text-theme-primary" href={`${baseUrl}`} rel="prefetch">
          {`<<`}
        </a>
        <a class="me-auto py-2 sm:hover:text-theme-primary" href={page.url.prev} rel="prefetch">
          {'←'}
        </a>
      </>
    ) : (
      <!-- <span class="ms-auto py-2">{`|`}</span> -->
    )
  }

  {
    [...Array(page.lastPage).keys()]
      .slice(Math.max(page.currentPage - 5, 0), Math.min(page.currentPage + 5, page.lastPage))
      .map(i => i + 1)
      .map(num => (
        <a
          href={`${baseUrl}/${num}`}
          class={`m-auto py-2 sm:hover:text-theme-primary ${
            page.currentPage === num ? 'font-bold text-theme-primary' : ''
          }`}
        >
          {num}
        </a>
      ))
  }

  {
    page.url.next ? (
      <>
        <a class="ms-auto py-2 sm:hover:text-theme-primary" href={page.url.next} rel="prefetch">
          {`→`}
        </a>
        <a
          class="ms-auto py-2 sm:hover:text-theme-primary"
          href={`${baseUrl}/${page.lastPage}`}
          rel="prefetch"
        >
          {`>>`}
        </a>
      </>
    ) : (
      <!-- <span class="ms-auto py-2">{`|`}</span> -->
    )
  }
</nav>
