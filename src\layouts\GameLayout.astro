---
import TableOfContents from '@/components/TableOfContents.vue';
import TagLink from '@/components/TagLink.astro';
import Layout from '@/layouts/Layout.astro';
import type { BlogGameResult } from '@/server/routers/blog';

interface Props {
  game: BlogGameResult;
}

const { game } = Astro.props;

const { gameName, shortDescription, ogImage, imageFinalWide, tierItems } = game;

const reviewed = tierItems.find(r => r.reviewTitle) as (typeof tierItems)[number] | null;
const reviewTitle = reviewed?.reviewTitle ?? '';
const title = gameName + (reviewTitle ? ` ${reviewTitle}` : '');

const publishDate = reviewed?.publishDate ?? new Date();
const publishDateString = typeof publishDate === 'string' ? publishDate : publishDate.toISOString();
const publishDateFormatted = reviewed?.publishDateFormatted;
---

<script>
  const scrollBtn = document.getElementById('to-top-btn') as HTMLButtonElement;
  const targetHeader = document.getElementById('blog-hero') as HTMLDivElement;

  function callback(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      // only show the scroll to top button when the heading is out of view
      scrollBtn.dataset.show = (!entry.isIntersecting).toString();
    });
  }

  scrollBtn.addEventListener('click', () => {
    document.documentElement.scrollTo({ top: 0, behavior: 'smooth' });
  });

  const observer = new IntersectionObserver(callback);
  observer.observe(targetHeader);
</script>

<Layout
  title={title}
  description={reviewed?.reviewContent || shortDescription || ''}
  ogImage={ogImage ?? ''}
  publishDateString={publishDateString}
>
  <div class="gap-x-10 lg:flex lg:items-start" data-pagefind-body>
    <article class="grow break-words">
      <div id="blog-hero">
        <h1 class="title mb-3 sm:mb-1" data-pagefind-meta="title">{title}</h1>
      </div>
      <div
        id="content"
        class="prose prose-sm prose-cactus prose-headings:font-semibold prose-headings:before:absolute prose-headings:before:-ms-4 prose-headings:before:text-primary prose-headings:before:content-['#'] prose-th:before:content-none mt-12"
      >
        <slot />
      </div>
    </article>

    <!-- Sidebar -->
    <aside class="sticky top-20 order-2 basis-64 lg:-me-32 lg:block">
      <img src={imageFinalWide} alt="Header" style="max-height: 200px;" />
      <div class="text-xl font-bold">{gameName}</div>
      <p class="text-sm">{shortDescription}</p>
      <hr class="mt-2 mb-2" />
      <time datetime={publishDateString}>{publishDateFormatted}</time>

      <!-- Table of Contents -->
      <TableOfContents client:visible class="mb-2" />
      {
        tierItems.map(
          item =>
            item.tierLane && (
              <div>
                <TagLink tierSetSlug={item.tierLane.tierSetSlug} tierLane={item.tierLane} />
              </div>
            ),
        )
      }
    </aside>
  </div>
  <!-- Back to Top -->
  <button
    id="to-top-btn"
    class="bg-surface-200 hover:border-surface-400 dark:bg-surface-700 fixed end-4 bottom-8 z-90 flex h-10 w-10 translate-y-28 items-center justify-center rounded-full border-2 border-transparent text-3xl opacity-0 transition-all duration-300 data-[show=true]:translate-y-0 data-[show=true]:opacity-100 sm:end-8 sm:h-12 sm:w-12"
    aria-label="Back to Top"
    data-show="false"
    ><svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      fill="none"
      viewBox="0 0 24 24"
      stroke-width="2"
      stroke="currentColor"
      class="h-6 w-6"
    >
      <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5"></path>
    </svg>
  </button>
</Layout>
