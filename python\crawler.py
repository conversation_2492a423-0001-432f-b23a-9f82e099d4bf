import marimo

__generated_with = "0.2.1"
app = marimo.App(width="full")


@app.cell
def __():
    import marimo as mo
    import pandas as pd
    import json
    import os
    import re
    import requests
    import traceback

    from pydash import get
    from datetime import datetime, UTC
    from bs4 import BeautifulSoup
    from dotenv import load_dotenv
    from supabase import create_client, Client
    from lexorank.lexorank import lexorank
    from requests.models import PreparedRequest
    from html import unescape
    from slugify import slugify

    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.support.wait import WebDriverWait
    from selenium.webdriver.support.select import Select
    from selenium.webdriver.common.by import By
    from selenium.common.exceptions import NoSuchElementException
    from selenium.webdriver.common.action_chains import ActionChains
    from seleniumwire import webdriver

    return (
        <PERSON>Chai<PERSON>,
        BeautifulSoup,
        By,
        Client,
        EC,
        NoSuchElementException,
        PreparedRequest,
        Select,
        UTC,
        WebDriverWait,
        create_client,
        datetime,
        get,
        json,
        lexorank,
        load_dotenv,
        mo,
        os,
        pd,
        re,
        requests,
        slugify,
        traceback,
        unescape,
        webdriver,
    )


@app.cell
def __(mo):
    limit_slider = mo.ui.slider(start=10, stop=50, step=10, value=50, debounce=True)

    mo.md(
        f"""
        Limit Slider: {limit_slider}
        """
    )
    return (limit_slider,)


@app.cell
def __(BeautifulSoup, UTC, datetime, json, limit_slider, mo, re, requests):
    # %%  Init global vars
    initial_events = {}
    get_event_list, set_event_list = mo.state([])

    # %% Run
    def crawl_events():
        limit = limit_slider.value
        url = "https://store.steampowered.com/news/collection/sales/"
        res = requests.get(url)
        if res.status_code <= 200:
            detail = res.text
            soup = BeautifulSoup(detail, features="html.parser")
            application_config = soup.select_one("#application_config")
            initial_events = json.loads(application_config.attrs.get("data-initialevents"))

        events_raw = initial_events.get("events")
        events_new = []

        for event in events_raw[0:limit]:
            json_data = json.loads(event.get("jsondata"))
            name: str = event.get("event_name")
            started_at = event.get("rtime32_start_time")
            ended_at = event.get("rtime32_end_time")
            sale_vanity_id = json_data.get("sale_vanity_id")
            steam_slug: str = re.sub(r"(?<!^)(?=[A-Z])", "-", sale_vanity_id).lower()

            # Add year-month in name & steam_slug
            started_at_date = datetime.fromtimestamp(started_at, UTC)
            year = started_at_date.strftime("%Y")
            month = started_at_date.strftime("%m")
            name = f"{name.removesuffix(year).rstrip()} {year}-{month}"
            steam_slug = f"{steam_slug.removesuffix(year).rstrip()}-{year}-{month}"

            sales_sections = json_data.get("sale_sections", [])
            games = set([])

            main_color = ""
            text_color = ""
            for section in sales_sections:
                if not main_color:
                    main_color = section.get("background_gradient_top")
                if not text_color:
                    text_color = section.get("tab_active_label_color") or section.get("label_color")
                capsules = section.get("capsules", [])
                for cap in capsules:
                    if cap.get("type") == "game":
                        games.add(cap.get("id"))

            processed = {
                "label": name,
                "steamSlug": steam_slug,
                "startedAt": started_at,
                "endedAt": ended_at,
                "month": month,
                "year": year,
                "mainColor": main_color,
                "textColor": text_color,
                "gamesCount": len(games),
                "games": list(games),
            }
            events_new.append(processed)

        events_new.append(
            {
                "label": "No Event",
                "steamSlug": "no-event",
                "gamesCount": 0,
                "games": [],
            }
        )
        set_event_list(events_new)

    run_button = mo.ui.button(
        label="Run",
        on_change=lambda _: crawl_events(),
    )

    clear_button = mo.ui.button(
        label="Clear",
        on_change=lambda _: set_event_list([]),
    )

    mo.vstack(
        [
            mo.md(f"Limit: {limit_slider.value}"),
            mo.hstack(
                [
                    run_button,
                    clear_button,
                ]
            ),
        ]
    )
    return (
        clear_button,
        crawl_events,
        get_event_list,
        initial_events,
        run_button,
        set_event_list,
    )


@app.cell
def __(get_event_list, mo):
    event_table = mo.ui.table(data=get_event_list(), label="Events", selection="single")

    (event_table if get_event_list() else "")
    return (event_table,)


@app.cell
def __(Client, create_client, load_dotenv, os):
    # %% Init supabase

    env_loaded = load_dotenv(dotenv_path="../.env")

    if not env_loaded:
        raise Exception("ENV is not loaded")

    envs = {
        "url": os.environ.get("PUBLIC_SUPABASE_URL"),
        "key": os.environ.get("PUBLIC_SUPABASE_ANON_KEY"),
    }

    supabase_url = envs.get("url")
    supabase_key = envs.get("key")
    if not supabase_url or not supabase_key:
        raise Exception("ENV url,key invalid")

    supabase: Client = create_client(supabase_url, supabase_key)
    return env_loaded, envs, supabase, supabase_key, supabase_url


@app.cell
def __(event_table, json, mo, pd):
    # %% Edit Event

    def rgba2hex(rgba: str | None) -> str:
        if not rgba:
            return "#ffffff"
        if not rgba.startswith("rgba("):
            return rgba
        parts = rgba.replace("rgba(", "").replace(")", "").split(",")
        r, g, b, a = int(parts[0]), int(parts[1]), int(parts[2]), float(parts[3])
        return f"#{r:02x}{g:02x}{b:02x}{int(a*255):02x}"

    selected_event = event_table.value[0] if len(event_table.value) else None
    game_ids_df = pd.DataFrame(selected_event.get("games")) if selected_event else None

    event_editor = (
        mo.md(
            """
    - slug: {slug}
    - label: {label}
    - mainColor: {mainColor}    textColor: {textColor}
    - description: {description}
    - games: {games}
        """
        ).batch(
            slug=mo.ui.text(value=rgba2hex(selected_event.get("steamSlug"))),
            label=mo.ui.text(value=rgba2hex(selected_event.get("label"))),
            mainColor=mo.ui.text(value=rgba2hex(selected_event.get("mainColor"))),
            textColor=mo.ui.text(value=rgba2hex(selected_event.get("textColor"))),
            description=mo.ui.text_area(
                value="\n".join(
                    [
                        f'SteamSlug: {selected_event.get("steamSlug")}',
                        f'StartedAt: {selected_event.get("startedAt")}',
                        f'EndedAt: {selected_event.get("endedAt")}',
                        f'Year: {selected_event.get("year")}',
                        f'Month: {selected_event.get("month")}',
                        f'Count: {selected_event.get("gamesCount")}',
                    ]
                )
            ),
            games=mo.ui.code_editor(value=json.dumps(selected_event.get("games")), language="json"),
        )
        if selected_event
        else ""
    )

    (
        mo.vstack(
            [
                mo.accordion({"Original": mo.tree(selected_event)}),
                event_editor,
            ]
        )
        if selected_event
        else ""
    )
    return event_editor, game_ids_df, rgba2hex, selected_event


@app.cell
def __(event_editor, lexorank, mo, selected_event, supabase):
    # %% Save Event
    get_event_lane, set_event_lane = mo.state(None)

    def save_event():
        created_objs = []

        ####### New lane in event
        _data = (
            supabase.table("tierLane")
            .select("slug,lexorank")
            .eq("tierSetSlug", "event")
            .order("lexorank")
            .limit(2)
            .execute()
        )
        existing_lanes = _data.data
        prev_rank = existing_lanes[0]["lexorank"] if len(existing_lanes) > 0 else None
        next_rank = existing_lanes[1]["lexorank"] if len(existing_lanes) > 1 else None
        new_rank, is_ok = lexorank(prev_rank, next_rank)

        event = event_editor.value
        newEventLane = {
            **event,
            "tierSetSlug": "event",
            "lexorank": new_rank,
        }
        try:
            _data = supabase.table("tierLane").upsert(newEventLane).execute()
            set_event_lane(_data.data[0])
            print("New lane in event Created:", newEventLane.get("slug"))
            print("Between:", existing_lanes)
        except:
            pass

    save_event_button = mo.ui.button(
        label="Save Event",
        on_change=lambda _: save_event(),
    )

    (
        mo.vstack(
            [
                mo.md(
                    f"""<div style='
        color:{event_editor.value.get('textColor')};
        background-color:{event_editor.value.get('mainColor')}';>
        {event_editor.value.get('label')}
        </div>"""
                ),
                mo.accordion({"To Save": mo.tree(event_editor.value)}),
                save_event_button,
            ]
        )
        if selected_event
        else ""
    )
    return get_event_lane, save_event, save_event_button, set_event_lane


@app.cell
def __(get_event_lane, mo):
    # %% Saved
    event_lane = get_event_lane()
    mo.accordion({"New Event Lane": mo.tree(items=event_lane)}) if event_lane else ""
    return (event_lane,)


@app.cell
def __(event_editor, json, mo):
    # % Clean game_list
    _game_raw = json.loads(event_editor.value.get("games")) if event_editor.value else []

    _game_list = []
    for game in _game_raw:
        if isinstance(game, int):
            game = {
                "steamId": game,
                "gameUrl": f"https://store.steampowered.com/app/{game}",
                "event": event_editor.value.get("slug") or event_editor.value.value.get("slug"),
            }

        _game_list.append(game)

    game_list = _game_list
    total_game_list = len(game_list)

    mo.ui.table(game_list, label="Games") if event_editor.value else ""
    return game, game_list, total_game_list


@app.cell
def __(game_list, mo, supabase):
    # % Get skipped_ids
    get_existing_ids, set_existing_ids = mo.state([])

    def fetch_skipped_ids():
        _data = supabase.table("gameSteam").select("steamId").execute()
        _exist_ids = [game.get("steamId") for game in _data.data]
        set_existing_ids(_exist_ids)

    fetch_skipped_ids_button = mo.ui.button(
        label="Fetch Skipped Ids",
        on_change=lambda _: fetch_skipped_ids(),
    )

    (
        mo.vstack(
            [
                fetch_skipped_ids_button,
            ]
        )
        if game_list
        else ""
    )
    return (
        fetch_skipped_ids,
        fetch_skipped_ids_button,
        get_existing_ids,
        set_existing_ids,
    )


@app.cell
def __(game_list, get_existing_ids, mo):
    skipped_ids = [game.get("steamId") for game in game_list if game.get("steamId") in get_existing_ids()]
    to_crawl_list = [game for game in game_list if game.get("steamId") not in get_existing_ids()]

    (
        mo.vstack(
            [
                mo.md(
                    f"""
                    To Crawl: {len(to_crawl_list)}/{len(game_list)}
                    """
                ),
                mo.accordion({"Skipped List": mo.tree(skipped_ids)}),
                mo.accordion({"To Crawl List": mo.tree(to_crawl_list)}),
            ]
        )
        if game_list
        else ""
    )
    return skipped_ids, to_crawl_list


@app.cell
def __(mo):
    need_steam_login_switch = mo.ui.switch(label="Need Steam Login ?")
    need_steam_login_switch
    return (need_steam_login_switch,)


@app.cell
def __(ActionChains, e, webdriver):
    # %% Get driver
    def get_driver(host: str = "http://selenoid:4444"):
        print(f"initiating driver on: {host}")

        driver: webdriver.Remote = None
        try:
            seleniumwire_options = {
                "auto_config": False,
                # the addr and the port where the proxy should start: -> starts it in the windmill container
                # "addr": "0.0.0.0",
                # "port": 9920,
            }

            options = webdriver.ChromeOptions()
            options.set_capability("browserName", "chrome")
            options.set_capability("selenoid:options", {"enableVNC": True})
            options.add_argument("--ignore-certificate-errors")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            # options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1920,1080")

            # options.add_argument(f'--proxy-server=host.docker.internal:{i}')
            # chrome_capabilities = {
            #     "browserName": "chrome",
            #     #"browserVersion": "91.0", #on Windows we can use the latest version by not specifying the version number
            #     "selenoid:options": {
            #         "enableVNC": True
            #     },
            #     'goog:chromeOptions': {'extensions': [],
            #                         'args': [f'--proxy-server=host.docker.internal:{i}',
            #                                     '--ignore-certificate-errors']
            #                         }
            # }

            driver = webdriver.Remote(
                command_executor="{}/wd/hub".format(host),
                options=options,
                seleniumwire_options=seleniumwire_options,
            )

            print(f"initiated successfully")
        except Exception as e:
            print(e)
            print(f"initiating driver failed")
            if driver:
                driver.close()

        action = ActionChains(driver)

        cursor_script = """
            var cursor = document.createElement('div');
            cursor.style.position = 'absolute';
            cursor.style.zIndex = '9999';
            cursor.style.width = '10px';
            cursor.style.height = '10px';
            cursor.style.borderRadius = '50%';
            cursor.style.backgroundColor = 'yellow';
            cursor.style.pointerEvents = 'none';
            document.body.appendChild(cursor);

            document.addEventListener('mousemove', function(e) {
                cursor.style.left = e.pageX - 5 + 'px';
                cursor.style.top = e.pageY - 5 + 'px';
            });
        """

        driver.execute_script(cursor_script)

        return driver, action

    return (get_driver,)


@app.cell
def __(mo):
    # %% Keep crawled info
    get_detail_map, set_detail_map = mo.state({})
    return get_detail_map, set_detail_map


@app.cell
def __(
    By,
    EC,
    NoSuchElementException,
    PreparedRequest,
    Select,
    WebDriverWait,
    get_detail_map,
    get_driver,
    mo,
    need_steam_login_switch,
    re,
    set_detail_map,
    skipped_ids,
    to_crawl_list,
    traceback,
):
    # %% Crawl

    need_steam_login = need_steam_login_switch.value
    _total = len(to_crawl_list)

    def crawl_games():
        # %% Prepare Driver
        driver, action = get_driver()

        # %% Wait for login
        if need_steam_login:
            login_url = "https://store.steampowered.com/login/"
            driver.get(login_url)
            WebDriverWait(driver, 120).until(EC.presence_of_element_located((By.CLASS_NAME, "user_avatar")))

        # %%
        _detail_map = get_detail_map() or {}

        # %%
        is_happy_flow = True
        for i in mo.status.progress_bar(range(len(to_crawl_list)), title="Crawling Games..."):
            row = to_crawl_list[i]
            original_steam_id: int = row.get("steamId")
            steam_id: int = original_steam_id
            game_name: str = row.get("gameName", "")
            game_url: str = row.get("gameUrl")
            print(f"====[crawler-deep] ({i+1}/{_total})====")
            print(f"URL: {game_url}")
            print(f"Title: {driver.title}")

            if steam_id in skipped_ids:
                print("Skipped: already in DB")
                continue

            if steam_id in _detail_map and _detail_map.get(steam_id) != {}:
                print("Skipped: already crawled this session")
                continue

            try:
                # Prepare URL
                params = {"l": "en"}
                req = PreparedRequest()
                req.prepare_url(game_url, params)
                req_url = req.url or game_url

                driver.get(req_url)
                new_game_detail = {
                    "gameName": game_name,
                    "hasAgeCheck": False,
                    "isAvailable": True,
                    "hasDemo": False,
                }

                try:
                    ## Find age select element by ID
                    year_select = Select(driver.find_element(By.ID, "ageYear"))
                    ## Select the option with the value "1990"
                    year_select.select_by_value("1990")
                except NoSuchElementException:
                    # Dropdown not found, skip and try to click
                    pass

                try:
                    ## Find the button element by ID
                    button = driver.find_element(By.ID, "view_product_page_btn")
                    ## Click the button
                    button.click()
                    new_game_detail["hasAgeCheck"] = True
                except NoSuchElementException:
                    # Button not found, skip select and click
                    pass

                try:
                    ## Find skippable error
                    span = driver.find_element(By.CSS_SELECTOR, "#error_box span")
                    print(span.text)
                    if "unavailable in your region" in span.text:
                        new_game_detail["isAvailable"] = False

                except NoSuchElementException:
                    pass

                # If it's a demo link redirected to the game, change them
                new_url = driver.current_url
                if str(steam_id) not in new_url:
                    print("redirected to:", new_url)
                    # Check removed game
                    if new_url in ["https://store.steampowered.com/"]:
                        new_game_detail["isAvailable"] = False

                    url = re.sub(r"\?.*$", "", new_url)
                    game_url = url

                    matches = re.search(r"app/(\d+)", url)

                    if matches:
                        new_id = int(matches.group(1))
                        steam_id = new_id

                new_game_detail.update(
                    {
                        "steamId": steam_id,
                        "gameUrl": game_url,
                    }
                )

                # Check if page not available, then skip
                if not new_game_detail.get("isAvailable"):
                    _detail_map[original_steam_id] = new_game_detail
                    continue

                # Check if page loaded, also get new game_name
                try:
                    name_id = "appHubAppName"
                    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, name_id)))
                    name_el = driver.find_element(By.ID, name_id)
                    new_game_detail["gameName"] = name_el.text
                except NoSuchElementException:
                    # Name not found, skip select and click
                    raise Exception("!!! Cant enter page !!!")

                # Demo Button not found, skip game
                try:
                    demo_indicator_selector = "#demoGameBtn, #freeGameBtn, .download_demo_button, .game_pt_note"
                    driver.find_element(By.CSS_SELECTOR, demo_indicator_selector)
                    new_game_detail["hasDemo"] = True
                except NoSuchElementException:
                    new_game_detail["hasDemo"] = False
                    try:
                        driver.find_element(By.CLASS_NAME, "btn_addtocart")
                        print(f"Skipped: released: {steam_id} ({i+1}/{_total})")

                    except NoSuchElementException:
                        print(f"Skipped: no demo: {steam_id} ({i+1}/{_total})")

                # Short Description
                short_description = ""
                try:
                    desc_class = "game_description_snippet"
                    WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.CLASS_NAME, desc_class)))

                    desc_element = driver.find_element(By.CLASS_NAME, desc_class)
                    short_description = (desc_element.get_attribute("innerHTML") or "").strip()
                except Exception:
                    print("No shortDescription")

                new_game_detail.update(
                    {
                        "shortDescription": short_description,
                    }
                )

                # About the Game
                about_the_game = ""
                try:
                    about_id = "game_area_description"
                    WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.ID, about_id)))

                    about_element = driver.find_element(By.ID, about_id)
                    about_the_game = (about_element.get_attribute("innerHTML") or "").strip()
                except Exception:
                    print("No aboutTheGame")

                new_game_detail.update(
                    {
                        "aboutTheGame": about_the_game,
                    }
                )

                # Developers
                developers = []
                try:
                    developer_selector = ".dev_row:nth-of-type(1) a"

                    developer_elements = driver.find_elements(By.CSS_SELECTOR, developer_selector)
                    developers = [
                        (developer_el.get_attribute("innerHTML") or "").strip() for developer_el in developer_elements
                    ]
                except Exception:
                    print("No developers")

                new_game_detail.update(
                    {
                        "developers": developers,
                    }
                )

                # Publishers
                publishers = []
                try:
                    publisher_selector = ".dev_row:nth-of-type(2) a"

                    publisher_elements = driver.find_elements(By.CSS_SELECTOR, publisher_selector)
                    publishers = [
                        (publisher_el.get_attribute("innerHTML") or "").strip() for publisher_el in publisher_elements
                    ]
                except Exception:
                    print("No publishers")

                new_game_detail.update(
                    {
                        "publishers": publishers,
                    }
                )

                # Tags
                tags = []
                try:
                    tag_selector = ".app_tag"
                    tag_elements = driver.find_elements(By.CSS_SELECTOR, tag_selector)

                    tag_selector = ".details_block span[data-panel] a"
                    tag_elements += driver.find_elements(By.CSS_SELECTOR, tag_selector)

                    tag_selector = ".game_area_details_specs_ctn .label:not(:has(.tooltip))"
                    tag_elements += driver.find_elements(By.CSS_SELECTOR, tag_selector)

                    tags = [(tag_el.get_attribute("innerHTML") or "").strip() for tag_el in tag_elements]
                    tags = list(set(tags))
                    tags.remove("+")
                except Exception:
                    print("No tags")

                new_game_detail.update(
                    {
                        "gameTags": tags,
                    }
                )

                # Videos
                vids = []
                vid_id = None
                try:
                    vid_selector = "div.highlight_movie"
                    WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.CSS_SELECTOR, vid_selector)))
                    vid_elements = driver.find_elements(By.CSS_SELECTOR, vid_selector)

                    for vid_el in vid_elements:
                        vid_id = re.search(r"(\d+)$", vid_el.id)
                        vid_id = vid_id.group(1) if vid_id else steam_id
                        vid = {
                            "id": vid_id,
                            "name": vid_el.get_attribute("data-video-title"),
                            "thumbnail": vid_el.get_attribute("data-poster"),
                            "webm": {
                                "480": vid_el.get_attribute("data-webm-source"),
                                "max": vid_el.get_attribute("data-webm-hd-source"),
                            },
                            "mp4": {
                                "480": vid_el.get_attribute("data-mp4-source"),
                                "max": vid_el.get_attribute("data-mp4-hd-source"),
                            },
                            "highlight": True,
                        }
                        vids.append(vid)
                except Exception:
                    print("No video")

                new_game_detail.update(
                    {
                        "movies": vids,
                    }
                )

                # Screenshots
                imgs = []
                try:
                    img_selector = "a.highlight_screenshot_link"
                    WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.CSS_SELECTOR, img_selector)))

                    img_elements = driver.find_elements(By.CSS_SELECTOR, img_selector)
                    for index, img_el in enumerate(img_elements):
                        img = {
                            "id": index,
                            "path_thumbnail": "",
                            "path_full": img_el.get_attribute("href"),
                        }
                        imgs.append(img)
                except Exception:
                    print("No screenshots")

                new_game_detail.update(
                    {
                        "screenshots": imgs,
                    }
                )

                _detail_map[original_steam_id] = new_game_detail

                print(f"Done: {game_url} ({i+1}/{_total})")
            except Exception:
                print(traceback.format_exc())
                print("Stopped: Found exception ⤴")
                is_happy_flow = False
                break

        set_detail_map(_detail_map)

        try:
            driver.close()
        except Exception:
            pass

        print("Done Crawling!")

    crawl_games_button = mo.ui.button(
        label=f"Crawl {_total} Games",
        on_change=lambda _: crawl_games(),
    )

    crawl_games_button if _total > 0 else ""
    return crawl_games, crawl_games_button, need_steam_login


@app.cell
def __(get_detail_map, mo):
    # %% Crawled Info
    detail_map = get_detail_map()
    mo.accordion({f"Game Info: {len(detail_map.values())}": mo.tree(items=detail_map)}) if detail_map else ""
    return (detail_map,)


@app.cell
def __(detail_map, e, game_list, mo, total_game_list):
    # %% Get final update list
    _update_game_steam_list = []

    for i in mo.status.progress_bar(range(len(game_list)), title="Get final update list..."):
        row = game_list[i]
        steam_id: int = row.get("steamId")
        game_name: str = row.get("gameName", "")
        # print(f"====[crawler-deep: extract] {steam_id} {game_name} ({i+1}/{total_game_list})====")
        _game = {}
        try:
            detail = detail_map.get(steam_id)
            if not detail:
                # print(f"Skipped no detail: {steam_id} ({i+1}/{total_game_list})")
                continue

            _game = {**row, **detail}

            tags = _game.get("gameTags", []) + detail.get("gameTags", [])
            tags = [t for t in list(set(tags))]
            _game["gameTags"] = tags

            # print(f"Extracted page: {game_name} ({i+1}/{total_game_list})")

            _update_game_steam_list.append(_game)

        except Exception as e:
            print(e)
            print(_game)
            print(f"Extract error for: {game_name} ({i+1}/{total_game_list})")
            break

    update_game_steam_list = _update_game_steam_list
    (
        mo.ui.table(
            data=[
                {
                    **u,
                    "aboutTheGame": u.get("aboutTheGame", "")[:20],
                    "shortDescription": u.get("shortDescription", "")[:20],
                }
                for u in update_game_steam_list
            ]
        )
        if len(update_game_steam_list)
        else ""
    )
    return detail, game_name, i, row, steam_id, tags, update_game_steam_list


@app.cell
def __(slugify, unescape):
    # %% Utils

    def fix_dict_keys(input_dict: dict):
        return {k[0].lower() + k[1:]: v for k, v in input_dict.items()}

    def generate_slug(obj: dict):
        game_name = obj.get("gameName")
        if not game_name:
            return obj

        slug = obj.get("slug")
        if not slug:
            slug = slugify(game_name)
            obj["slug"] = slug
            game_name_en = slugify(game_name, lowercase=False, separator=" ", allow_unicode=True)
            obj["gameNameEn"] = game_name_en
        return obj

    def clean_json(obj: dict):
        fields = [
            "gameTags",
            "screenshots",
            "movies",
            "publishers",
            "developers",
            "awards",
            "nominations",
        ]
        for field in fields:
            if field in obj:
                if not obj[field]:
                    obj[field] = []
                cleaned = [unescape(x) for x in obj[field]]
                obj[field] = cleaned
        return obj

    def extend_list(src: dict, obj: dict):
        fields = ["gameTags", "publishers", "developers", "awards", "nominations"]
        for field in fields:
            if field in obj:
                a = src[field] if isinstance(src.get(field), list) else []
                b = obj[field] if isinstance(obj.get(field), list) else []
                combined = list(set(a + b))
                obj[field] = combined
        return obj

    return clean_json, extend_list, fix_dict_keys, generate_slug


@app.cell
def __(
    clean_json,
    extend_list,
    fix_dict_keys,
    game_list,
    generate_slug,
    mo,
    supabase,
    update_game_steam_list,
):
    # %% Save GameSteams
    _total = len(update_game_steam_list)
    get_game_steam_created_objs, set_game_steam_created_objs = mo.state([])

    def save_game_steam():
        set_game_steam_created_objs([])

        for i in range(len(update_game_steam_list)):
            game = fix_dict_keys(game_list[i])

            print(f"===[game-to-db] {game.get('steamId')} {game.get('gameName')} ({i+1}/{_total})===")

            game = generate_slug(game)

            data = supabase.table("gameSteam").select("*").eq("steamId", game.get("steamId")).execute()

            if len(data.data) > 0:
                existing = data.data[0]
                game = extend_list(existing, game)

            game = clean_json(game)

            gameSteam = {k: v for k, v in game.items() if k not in ["event"]}

            created_game = supabase.table("gameSteam").upsert(gameSteam).execute()

            set_game_steam_created_objs([*get_game_steam_created_objs(), created_game.data])

        print("DONE !!")

    save_game_steam_button = mo.ui.button(
        label=f"Save {_total} gameSteams",
        on_change=lambda _: save_game_steam(),
    )

    mo.vstack([save_game_steam_button]) if len(update_game_steam_list) > 0 else ""
    return (
        get_game_steam_created_objs,
        save_game_steam,
        save_game_steam_button,
        set_game_steam_created_objs,
    )


@app.cell
def __(get_game_steam_created_objs, mo):
    _objs = get_game_steam_created_objs()
    mo.accordion({f"GameSteams: {len(_objs)}": _objs})
    return


@app.cell
def __(fix_dict_keys, game_list, lexorank, mo, supabase, total_game_list):
    # %% Save TierItems
    _total = total_game_list
    get_tier_item_created_objs, set_tier_item_created_objs = mo.state([])

    def save_tier_items():
        set_tier_item_created_objs([])
        created_objs = []
        rank = "0|iiiiiiiiii"
        max_rank = "0|zzzzzzzzzz"

        for i in range(len(game_list)):
            game = fix_dict_keys(game_list[i])
            steam_id = game.get("steamId")
            game_name = game.get("gameName")

            print(f"===[item-to-db] {steam_id} {game_name} ({i+1}/{_total})===")

            tierItem = {}
            try:
                data = (
                    supabase.table("tierItem")
                    .select("gameSteamId")
                    .eq("type", "GAME_STEAM")
                    .eq("tierSetSlug", "event")
                    .eq("tierLaneSlug", game.get("event"))
                    .eq("gameSteamId", steam_id)
                    .execute()
                )

                # Skip existed
                if len(data.data) > 0:
                    print("Skip existed")
                    continue

                tierItem = {
                    "type": "GAME_STEAM",
                    "tierSetSlug": "event",
                    "tierLaneSlug": game.get("event"),
                    "gameSteamId": game.get("steamId"),
                    "lexorank": rank,
                }
                rank, ok = lexorank(rank, max_rank)
                created_item = supabase.table("tierItem").insert(tierItem).execute()

                set_tier_item_created_objs([*get_tier_item_created_objs(), created_item.data])

                print("Done inserted:", created_item.data)
            except Exception:
                print("Cant insert:", tierItem)
                import traceback

                print(traceback.print_exc())

    save_tier_items_button = mo.ui.button(
        label=f"Save {_total} tierItems",
        on_change=lambda _: save_tier_items(),
    )

    save_tier_items_button if _total > 0 else ""
    return (
        get_tier_item_created_objs,
        save_tier_items,
        save_tier_items_button,
        set_tier_item_created_objs,
    )


@app.cell
def __(get_tier_item_created_objs, mo):
    _objs = get_tier_item_created_objs()
    mo.accordion({f"TierItems: {len(_objs)}": _objs})
    return


if __name__ == "__main__":
    app.run()
