/*
  Warnings:

  - You are about to drop the column `userId` on the `tierSet` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "tierSet" DROP COLUMN "userId",
ADD COLUMN     "spaceId" INTEGER;

-- CreateTable
CREATE TABLE "player" (
    "id" SERIAL NOT NULL,
    "displayName" TEXT NOT NULL DEFAULT '',
    "userId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "player_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "space" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "label" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "space_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayersOnSpaces" (
    "playerId" INTEGER NOT NULL,
    "spaceId" INTEGER NOT NULL,
    "assignedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PlayersOnSpaces_pkey" PRIMARY KEY ("playerId","spaceId")
);

-- CreateIndex
CREATE UNIQUE INDEX "player_id_key" ON "player"("id");

-- CreateIndex
CREATE UNIQUE INDEX "player_userId_key" ON "player"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "space_id_key" ON "space"("id");

-- CreateIndex
CREATE UNIQUE INDEX "space_slug_key" ON "space"("slug");

-- AddForeignKey
ALTER TABLE "tierSet" ADD CONSTRAINT "tierSet_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayersOnSpaces" ADD CONSTRAINT "PlayersOnSpaces_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayersOnSpaces" ADD CONSTRAINT "PlayersOnSpaces_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
