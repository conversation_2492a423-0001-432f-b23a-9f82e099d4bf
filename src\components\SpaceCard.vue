<script setup lang="ts">
import type { Selectable } from 'kysely';

import type { KyselyDatabase } from '@/database/kysely';

import BaseContentCard from './BaseContentCard.vue';

const props = defineProps({
  space: {
    type: Object as () => Selectable<KyselyDatabase['tierSet']> & {
      playersOnSpaces: { player: { displayName: string } }[];
    },
    required: true,
  },
});
const emit = defineEmits();

const targetUrl = computed(() => {
  const url = `/spaces/${props.space.slug}/`;
  return url;
});

const backgroundUrl = computed(() => {
  const url = `/og-image/spaces/${props.space.slug}.png`;
  return url;
});
</script>

<template>
  <BaseContentCard :url="targetUrl" :bg="backgroundUrl">
    <template #title>
      {{ props.space?.label ?? '' }}
    </template>
    <template #body>
      <span class="text-sm opacity-70">by </span>
      <span>
        {{ props.space.playersOnSpaces.map(ps => ps.player.displayName).join(', ') }}
      </span>
    </template>
  </BaseContentCard>
</template>
