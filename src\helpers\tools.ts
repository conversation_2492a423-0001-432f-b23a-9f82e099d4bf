export interface Tool {
  label: string;
  slug: string;
  desc: string;
  icon: string;
  authed?: boolean;
  soon?: boolean;
}

export const TOOLS: Tool[] = [
  {
    label: 'Tier Maker S',
    slug: 'simple-tier',
    desc: 'Simple, use any image you want',
    icon: 'lucide:list-ordered',
    authed: false,
  },
  {
    label: 'Crafting Table',
    slug: 'craft',
    desc: 'Create new items from your imagination',
    icon: 'lucide:pickaxe',
    authed: true,
  },
  // {
  //   label: 'Battle',
  //   slug: 'battle',
  //   desc: 'Randomly pick entry to 1v1 until last one standing',
  //   icon: 'lucide:swords',
  //   soon: true,
  // },
  {
    label: "<PERSON><PERSON><PERSON>'s office",
    slug: 'demoman',
    desc: 'Find out what is he going to play next',
    icon: 'lucide:bomb',
    authed: false,
  },
];
