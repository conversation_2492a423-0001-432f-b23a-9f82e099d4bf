/*
  Warnings:

  - You are about to drop the column `imageBroadcastLeftPanel` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageBroadcastRightPanel` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageCapsuleLg` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageCapsuleSm` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageHeader` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageHeroCapsule` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageLibraryHero` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageLibraryPoster` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imageLogo` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imagePageBgBlur` on the `gameSteam` table. All the data in the column will be lost.
  - You are about to drop the column `imagePageBgRaw` on the `gameSteam` table. All the data in the column will be lost.

*/

-- AlterTable
ALTER TABLE "gameSteam"
ADD COLUMN     "hasImageBroadcastLeftPanel" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageBroadcastRightPanel" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageCapsuleLg" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageCapsuleSm" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageHeader" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageHeroCapsule" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageLibraryHero" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageLibraryPoster" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImageLogo" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImagePageBgBlur" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "hasImagePageBgRaw" BOOLEAN NOT NULL DEFAULT true;

-- Move values
UPDATE "gameSteam"
SET 
  "hasImageLogo" = ("imageLogo" <> ''),
  "hasImageCapsuleSm" = ("imageCapsuleSm" <> ''),
  "hasImageCapsuleLg" = ("imageCapsuleLg" <> ''),
  "hasImageHeader" = ("imageHeader" <> ''),
  "hasImageHeroCapsule" = ("imageHeroCapsule" <> ''),
  "hasImageLibraryPoster" = ("imageLibraryPoster" <> ''),
  "hasImageLibraryHero" = ("imageLibraryHero" <> ''),
  "hasImagePageBgBlur" = ("imagePageBgBlur" <> ''),
  "hasImagePageBgRaw" = ("imagePageBgRaw" <> ''),
  "hasImageBroadcastLeftPanel" = ("imageBroadcastLeftPanel" <> ''),
  "hasImageBroadcastRightPanel" = ("imageBroadcastRightPanel" <> '');

-- AlterTable
ALTER TABLE "gameSteam" 
DROP COLUMN "imageBroadcastLeftPanel",
DROP COLUMN "imageBroadcastRightPanel",
DROP COLUMN "imageCapsuleLg",
DROP COLUMN "imageCapsuleSm",
DROP COLUMN "imageHeader",
DROP COLUMN "imageHeroCapsule",
DROP COLUMN "imageLibraryHero",
DROP COLUMN "imageLibraryPoster",
DROP COLUMN "imageLogo",
DROP COLUMN "imagePageBgBlur",
DROP COLUMN "imagePageBgRaw";