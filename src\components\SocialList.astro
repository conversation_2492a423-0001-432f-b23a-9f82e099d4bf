---
import { SOCIAL_LINKS } from '@/helpers/constants';

// *** SVG icons from https://tablericons.com/ ***
---

<div class="flex flex-wrap items-center gap-x-4 sm:items-center">
  <!-- <p>Contact me:</p> -->
  <ul class="flex flex-1 items-center gap-x-4 sm:flex-initial">
    <li>
      <a
        class="sm:hover:text-link inline-block p-2"
        href={SOCIAL_LINKS.discord}
        target="_blank"
        rel="noopener noreferrer"
      >
        <svg
          class="h-6 w-6"
          aria-hidden="true"
          focusable="false"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"> </path>
          <circle cx="9" cy="12" r="1"> </circle>
          <circle cx="15" cy="12" r="1"> </circle>
          <path d="M7.5 7.5c3.5 -1 5.5 -1 9 0"> </path>
          <path d="M7 16.5c3.5 1 6.5 1 10 0"> </path>
          <path
            d="M15.5 17c0 1 1.5 3 2 3c1.5 0 2.833 -1.667 3.5 -3c.667 -1.667 .5 -5.833 -1.5 -11.5c-1.457 -1.015 -3 -1.34 -4.5 -1.5l-1 2.5"
          >
          </path>
          <path
            d="M8.5 17c0 1 -1.356 3 -1.832 3c-1.429 0 -2.698 -1.667 -3.333 -3c-.635 -1.667 -.476 -5.833 1.428 -11.5c1.388 -1.015 2.782 -1.34 4.237 -1.5l1 2.5"
          >
          </path>
        </svg>
        <span class="sr-only">Discord</span>
      </a>
    </li>
    <li>
      <a class="sm:hover:text-link inline-block p-2" href={SOCIAL_LINKS.youtube}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="icon icon-tabler icon-tabler-brand-youtube"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="#ffffff"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <rect x="3" y="5" width="18" height="14" rx="4"></rect>
          <path d="M10 9l5 3l-5 3z"></path>
        </svg>
        <span class="sr-only">Youtube</span>
      </a>
    </li>
    <!-- <li>
      <a class="sm:hover:text-link inline-block p-2" href={`mailto:${SOCIAL_LINKS.email}`}>
        <svg
          class="h-6 w-6"
          aria-hidden="true"
          focusable="false"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <rect x="3" y="5" width="18" height="14" rx="2"></rect>
          <polyline points="3 7 12 13 21 7"></polyline>
        </svg>
        <span class="sr-only">Email</span>
      </a>
    </li> -->
  </ul>
</div>
