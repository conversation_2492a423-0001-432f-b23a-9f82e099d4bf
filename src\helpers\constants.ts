export const CONST = {
  // Site url for RSS, astro.config.ts
  siteUrl: 'https://demoman.munverse.me/',
  // Used as both a meta property (src/components/BaseHead.astro L:31 + L:49) & the generated satori png (src/pages/og-image/[slug].png.ts)
  author: 'https://youtube.com/@-yay',
  // Meta property used to construct the meta title property, found in src/components/BaseHead.astro L:11
  title: 'Demoman Blog',
  titleExt: 'รีวิวเดโมเกมเยอะแยะ',
  // Meta property used as a default description meta property
  description:
    'เครื่องมือหลากหลายที่ช่วยคุณจัดอันดับ และแหล่งรวมรีวิวเกมอินดี้ เดโมเกมเยอะแยะ ที่คุณไม่น่าจะเคยเห็น',
  // HTML lang property, found in src/layouts/Base.astro L:18
  lang: 'th-TH',
  // Meta property, found in src/components/BaseHead.astro L:42
  ogLocale: 'th_TH',
  // Sets the meta data theme-color, found in src/components/BaseHead.astro L:34. Toggling the dark mode will update the meta content with either light/dark color, implementation in src/layouts/Base.astro L:41.
  themeColorLight: '#fafafa',
  themeColorDark: '#1d1f21',
  // Date.prototype.toLocaleDateString() parameters, found in src/utils/date.ts.
  date: {
    locale: 'en-GB',
    options: {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    },
  },
};

export const MENU_LINKS = [
  {
    title: 'Home',
    path: '/',
  },
  {
    title: 'Blog',
    path: '/blog',
  },
  {
    title: 'Tiers',
    path: '/tiers',
  },
  {
    title: 'Spaces',
    path: '/spaces',
  },
  {
    title: 'Tools',
    path: '/tools',
  },
];

// ! Remember to add your own socials
export const SOCIAL_LINKS = {
  youtube: 'https://youtube.com/@-yay',
  discord: 'https://discord.gg/UMrvxwgCa5',
  email: '<EMAIL>',
};
