---
import { slugifyWithSettings } from '@/helpers/utils';

export interface Props {
  readonly tierLane: {
    tierSetSlug: string;
    label: string;
    slug: string;
    mainColor: string;
    textColor: string;
    icon: string;
  };
  readonly tierSetSlug?: string;
  readonly header?: boolean;
  readonly noClick?: boolean;
}

const { tierSetSlug, tierLane, header = false, noClick = false } = Astro.props;

const label = tierLane?.label ?? '';
const tierLaneSlug = tierLane?.slug ?? slugifyWithSettings(label);

const mainColor = tierLane?.mainColor;
const textColor = tierLane?.textColor ?? '#fff';
const icon = tierLane?.icon ?? '';
---

<div
  class:list={[
    `inline w-fit rounded-md px-2 leading-6 font-bold`,
    `criteria-${tierSetSlug}`,
    `criteria-${tierSetSlug}-${tierLaneSlug}`,
    { 'py-0.5 text-sm hover:ring-1 hover:ring-slate-500': header },
    { 'text-xs opacity-50 hover:opacity-100': !header },
    { 'before:content-["#"]': !icon },
  ]}
  style={{
    color: textColor,
    backgroundImage: mainColor
      ? `linear-gradient(to right , ${mainColor} 60%, rgb(var(--theme-text-rgb) / 0.5));`
      : '',
    pointerEvents: noClick ? 'none' : 'unset',
  }}
  data-pagefind-filter={`${label}`}
  data-pagefind-sort={`${label}[data-${tierSetSlug}]`}
  {...{ [`data-${tierSetSlug}`]: tierLaneSlug }}
>
  <a
    href={`/blog/tiers/${tierSetSlug}/${tierLaneSlug}`}
    aria-label={`View more games with ${tierSetSlug} ${tierLaneSlug}`}
    aria-disabled={noClick}
    class="whitespace-pre-wrap"
  >
    <span>{icon}</span>
    <slot>{label}</slot>
  </a>
</div>
