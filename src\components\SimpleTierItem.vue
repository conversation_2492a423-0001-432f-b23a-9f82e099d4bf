<script setup lang="ts">
import * as _ from 'lodash-es';
import Galleria from 'primevue/galleria';

import { Icon } from '@iconify/vue';
import { useElementVisibility, useVModels } from '@vueuse/core';

import { cn } from '@/helpers/cn';
import { compressImageToBase64 } from '@/helpers/file-compressor';
import { detectIfFileImage, detectIfFileVideo } from '@/helpers/utils';
import type { SimpleTierItemObject } from '@/server/routers/simpleTier';

const props = defineProps({
  tierItem: {
    type: Object as () => SimpleTierItemObject,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  isMeSelected: {
    type: Boolean,
    default: false,
  },
  isCompact: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'update:tierItem', val: SimpleTierItemObject): void;
}>();

const { tierItem } = useVModels(props, emit);

const cardRef = ref<HTMLElement>();
const isLoaded = ref(false);
const isVisible = useElementVisibility(cardRef);

const isShowingGallery = ref(false);
const activeIndex = ref(0);

const previews = computed(() =>
  [
    tierItem.value.imgData,
    tierItem.value.imgUrl,
    ...(tierItem.value.refs || [])?.map(ref => ref.url),
  ].filter(v => v),
);

function showGalleryIndex(index: number) {
  activeIndex.value = index;
  isShowingGallery.value = true;
}

async function onImageLoaded(e: Event) {
  isLoaded.value = true;
  const img = e.currentTarget as HTMLImageElement;

  if (img.src.startsWith('data:')) {
    const imgData = await compressImageToBase64(await fetch(img.src).then(res => res.blob()));
    if (imgData) {
      tierItem.value.imgData = imgData;
    }
  }
}
</script>

<template>
  <!-- Card -->
  <div
    ref="cardRef"
    :class="
      cn(
        'group/itemcard relative h-full w-full rounded',
        'ring-(--main)',
        { 'border-2 border-orange-400': isMeSelected },
        !isCompact ? 'h-[100px] min-h-[100px]' : 'h-[40px] min-h-[40px]',
      )
    "
  >
    <!-- Glowing BG -->
    <!-- <div
      :class="cn(
        'absolute -z-10 opacity-50 -inset-px',
        'duration-1000 transition-all',
        'bg-(--main) rounded-xl blur-md',
        'group-hover/itemcard:opacity-70 group-hover/itemcard:blur-lg',
        'group-hover/itemcard:duration-200 group-hover/itemcard:animate-pulse',
      )"
    ></div> -->
    <div
      v-if="isLoaded || isVisible"
      :class="cn('relative h-full w-full overflow-hidden rounded', { invisible: !isVisible })"
    >
      <Galleria
        :value="previews"
        :showItemNavigators="previews.length > 1"
        :showItemNavigatorsOnHover="true"
        :showThumbnails="false"
        :pt="{
          root: {
            class: cn('h-full w-full cursor-grab hover:scale-105 hover:transition-all'),
          },
          content: {
            class: cn('h-full'),
          },
          itemsContainer: {
            class: cn('h-full'),
          },
          item: {
            class: cn('h-full'),
          },
          prevButton: {
            class: cn('z-50 h-8 w-4 p-0'),
          },
          nextButton: {
            class: cn('z-50 h-8 w-4 p-0'),
          },
        }"
      >
        <template #item="{ item }: { item: string }">
          <div @click="showGalleryIndex(previews.indexOf(item))" :class="cn('h-full w-full')">
            <img
              v-if="detectIfFileImage(item)"
              :src="item"
              :alt="`${tierItem.label} ${index + 1}`"
              :class="cn('h-full w-full max-w-[unset] object-cover')"
              @load="onImageLoaded"
            />
            <video
              v-else-if="detectIfFileVideo(item)"
              :src="item"
              :class="cn('h-full max-w-[unset] object-cover')"
              autoplay
              loop
            ></video>
            <iframe v-else :src="item" :class="cn('h-full w-full')"></iframe>
          </div>
        </template>
      </Galleria>

      <div
        v-if="tierItem.label"
        :class="
          cn(
            'group/gameinfo',
            'absolute bottom-0 h-3 w-full overflow-hidden',
            'ignore-drag cursor-pointer text-(--text)',
            'transition-height hover:h-full',
          )
        "
        :title="tierItem.label"
      >
        <div
          :class="
            cn(
              'absolute left-0 top-0 z-0 h-full w-full bg-(--main)',
              'transition-opacity group-hover/gameinfo:opacity-80',
              tierItem.label ? 'opacity-100' : 'opacity-30',
            )
          "
        ></div>
        <span
          :class="
            cn(
              'absolute left-0 top-0 z-10 h-full w-full',
              'text-ellipsis text-xxs font-semibold group-hover/gameinfo:text-sm',
            )
          "
        >
          <a :href="tierItem.urls?.[0]" target="_blank">
            {{ tierItem.label }}
          </a>
          <a v-for="url in tierItem.urls" :key="url" :href="url" target="_blank">
            <Icon icon="lucide:external-link" class="" />
          </a>
        </span>
      </div>
    </div>
  </div>

  <Galleria
    v-model:visible="isShowingGallery"
    v-model:activeIndex="activeIndex"
    :value="previews"
    :full-screen="true"
    :showItemNavigators="previews.length > 1"
    :showItemNavigatorsOnHover="true"
    :showThumbnails="false"
    :pt="{
      prevButton: {
        class: cn('z-50'),
      },
      nextButton: {
        class: cn('z-50'),
      },
    }"
  >
    <template #item="{ item }: { item: string }">
      <div :class="cn('max-h-[90vh] min-h-[10vh] min-w-[50vw] max-w-[90vw]')">
        <img
          v-if="detectIfFileImage(item)"
          :src="item"
          :alt="tierItem.label"
          :class="cn('h-full w-full object-cover')"
        />
        <video
          v-else-if="detectIfFileVideo(item)"
          :src="item"
          :class="cn('h-full w-full object-cover')"
          autoplay
          loop
          controls
        ></video>
        <iframe v-else :src="item" :class="cn('h-full w-full')"></iframe>
      </div>
    </template>
  </Galleria>
</template>
