import { PrismaClient } from '@prisma/client';

import gameEvent from './game_event.js';
import tierLane from './tier_lane.js';
import tierSet from './tier_set.js';

const prisma = new PrismaClient();

const load = async () => {
  try {
    await prisma.tierSet.deleteMany();
    console.log('Deleted records in tierSet table');
    await prisma.tierLane.deleteMany();
    console.log('Deleted records in tierLane table');
    await prisma.gameEvent.deleteMany();
    console.log('Deleted records in gameEvent table');

    // await prisma.$queryRaw`ALTER TABLE game_event AUTO_INCREMENT = 1`;
    // console.log("reset gameEvent auto increment to 1");
    // await prisma.$queryRaw`ALTER TABLE game_tier AUTO_INCREMENT = 1`;
    // console.log("reset tierLane auto increment to 1");
    // await prisma.$queryRaw`ALTER TABLE game_tierset AUTO_INCREMENT = 1`;
    // console.log("reset tierSet auto increment to 1");

    await prisma.tierSet.createMany({ data: tierSet });
    console.log('Added tierSet data');
    await prisma.tierLane.createMany({ data: tierLane });
    console.log('Added tierLane data');
    await prisma.gameEvent.createMany({ data: gameEvent });
    console.log('Added gameEvent data');
  } catch (e) {
    console.error(e);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
};

load();
