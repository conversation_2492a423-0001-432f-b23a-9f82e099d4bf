/*
  Warnings:

  - Made the column `note` on table `tierItem` required. This step will fail if there are existing NULL values in that column.
  - Made the column `reviewContent` on table `tierItem` required. This step will fail if there are existing NULL values in that column.
  - Made the column `reviewTitle` on table `tierItem` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "TierItemType" AS ENUM ('NORMAL', 'GAME_STEAM');

-- AlterTable
ALTER TABLE "tierItem" ADD COLUMN     "displayUrls" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "targetUrl" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "type" "TierItemType" NOT NULL DEFAULT 'NORMAL',
ALTER COLUMN "note" SET NOT NULL,
ALTER COLUMN "reviewContent" SET NOT NULL,
ALTER COLUMN "reviewTitle" SET NOT NULL;
