generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "views", "postgresqlExtensions"]
}

generator kysely {
  provider = "prisma-kysely"
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  directUrl  = env("DIRECT_URL")
  extensions = [pgvector(map: "vector", schema: "extensions")] // Add the pgvector extension
}

model GameSteam {
  id                          Int                          @unique @default(autoincrement())
  gameName                    String                       @default("")
  gameNameEn                  String                       @default("")
  gameUrl                     String                       @default("")
  steamId                     Int                          @id @unique
  slug                        String                       @default("")
  gameTags                    String[]                     @default([])
  recordedVideo               String[]                     @default([])
  nominations                 String[]                     @default([])
  awards                      String[]                     @default([])
  releaseDate                 DateTime?
  shortDescription            String                       @default("")
  detailedDescription         String                       @default("")
  aboutTheGame                String                       @default("")
  website                     String                       @default("")
  publishers                  String[]                     @default([])
  developers                  String[]                     @default([])
  movies                      Json[]                       @default([])
  screenshots                 Json[]                       @default([])
  hasAgeCheck                 Boolean                      @default(false)
  createdAt                   DateTime                     @default(now())
  updatedAt                   DateTime                     @default(now()) @updatedAt
  hasDemo                     Boolean                      @default(false)
  isAvailable                 Boolean                      @default(true)
  hasImageBroadcastLeftPanel  Boolean                      @default(true)
  hasImageBroadcastRightPanel Boolean                      @default(true)
  hasImageCapsuleLg           Boolean                      @default(true)
  hasImageCapsuleSm           Boolean                      @default(true)
  hasImageHeader              Boolean                      @default(true)
  hasImageHeroCapsule         Boolean                      @default(true)
  hasImageLibraryHero         Boolean                      @default(true)
  hasImageLibraryPoster       Boolean                      @default(true)
  hasImageLogo                Boolean                      @default(true)
  hasImagePageBgBlur          Boolean                      @default(true)
  hasImagePageBgRaw           Boolean                      @default(true)
  demoId                      Int?
  embedding                   Unsupported("vector(1024)")?
  fts                         Unsupported("tsvector")?
  tierItems                   TierItem[]

  @@index([gameName])
  @@index([embedding])
  @@index([fts], type: Gin)
  @@map("gameSteam")
}

model TierItem {
  id                   Int          @id @unique @default(autoincrement())
  gameSteamId          Int?
  tierLaneSlug         String?
  lexorank             String?
  note                 String       @default("")
  createdAt            DateTime     @default(now())
  updatedAt            DateTime     @default(now()) @updatedAt
  reviewContent        String       @default("")
  reviewTitle          String       @default("")
  tierSetSlug          String
  publishDate          DateTime?
  displayUrls          String[]     @default([])
  targetUrl            String       @default("")
  type                 TierItemType @default(NORMAL)
  fromLaneSlug         String?
  fromLaneTierSetSlug  String?
  clonedFromTierItemId Int?
  tsv                  String       @default("")
  clonedFromTierItem   TierItem?    @relation("cloned", fields: [clonedFromTierItemId], references: [id], onDelete: SetDefault)
  clonedToTierItems    TierItem[]   @relation("cloned")
  fromLane             TierLane?    @relation("itemTierLane", fields: [fromLaneTierSetSlug, fromLaneSlug], references: [tierSetSlug, slug], onDelete: Cascade)
  gameSteam            GameSteam?   @relation(fields: [gameSteamId], references: [steamId], onDelete: SetDefault)
  tierSet              TierSet      @relation(fields: [tierSetSlug], references: [slug], onDelete: Cascade)
  tierLane             TierLane?    @relation(fields: [tierSetSlug, tierLaneSlug], references: [tierSetSlug, slug], onDelete: Cascade)

  @@unique([fromLaneTierSetSlug, fromLaneSlug, tierSetSlug])
  @@index([reviewTitle])
  @@map("tierItem")
}

model TierLane {
  id          Int        @unique @default(autoincrement())
  slug        String
  label       String     @default("")
  icon        String     @default("")
  mainColor   String     @default("#000")
  textColor   String     @default("#fff")
  description String     @default("")
  score       Int        @default(0)
  lexorank    String?
  tierSetSlug String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @default(now()) @updatedAt
  toTierItems TierItem[] @relation("itemTierLane")
  tierItems   TierItem[]
  tierSet     TierSet    @relation(fields: [tierSetSlug], references: [slug], onDelete: Cascade)

  @@id([tierSetSlug, slug], name: "tierLaneIdentifier")
  @@unique([tierSetSlug, slug])
  @@map("tierLane")
}

model TierSet {
  id             Int            @unique @default(autoincrement())
  slug           String         @id @unique
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @default(now()) @updatedAt
  description    String         @default("")
  icon           String         @default("")
  label          String         @default("")
  mainColor      String         @default("#000")
  textColor      String         @default("#fff")
  spaceId        Int?
  permissionType PermissionType @default(PUBLIC)
  tierItems      TierItem[]
  tierLanes      TierLane[]
  space          Space?         @relation(fields: [spaceId], references: [id], onDelete: SetDefault)

  @@map("tierSet")
}

model Player {
  id              Int               @id @unique @default(autoincrement())
  displayName     String            @default("")
  userId          String            @unique @db.Uuid
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @default(now()) @updatedAt
  playersOnSpaces PlayersOnSpaces[]

  @@map("player")
}

model Space {
  id              Int               @id @unique @default(autoincrement())
  slug            String            @unique
  label           String            @default("")
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @default(now()) @updatedAt
  playersOnSpaces PlayersOnSpaces[]
  tierSets        TierSet[]

  @@map("space")
}

model PlayersOnSpaces {
  playerId   Int
  spaceId    Int
  assignedBy String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now()) @updatedAt
  player     Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)
  space      Space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)

  @@id([playerId, spaceId])
  @@map("playersOnSpaces")
}

enum TierItemType {
  NORMAL
  GAME_STEAM
  FROM_LANE
}

enum PermissionType {
  PRIVATE
  PUBLIC
}
