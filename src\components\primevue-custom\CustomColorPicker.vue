<script setup lang="ts">
import PrimeColorPicker from 'primevue/colorpicker';

import { useVModels } from '@vueuse/core';

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:lane', 'delete:lane']);

const { modelValue } = useVModels(props, emit);

const cleanedValue = computed({
  get() {
    const trimmed = modelValue.value.replace('#', '');
    const filled =
      trimmed.length > 3
        ? trimmed
        : trimmed
            .split('')
            .flatMap(c => c.repeat(2))
            .join('');
    return filled;
  },
  set(val) {
    modelValue.value = `#${val}`;
  },
});
</script>

<template>
  <PrimeColorPicker
    v-model="cleanedValue"
    inputId="cp-hex"
    format="hex"
    :pt="{
      input: {
        class: 'border-2! border-(--text)!',
      },
    }"
    :baseZIndex="100"
  />
</template>
