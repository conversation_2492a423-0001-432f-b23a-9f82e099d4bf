<script setup lang="ts">
import { format } from 'date-fns';

import TagLink from '@/components/TagLink.vue';
import TierItemLinks from '@/components/TierItemLinks.vue';
import { cn } from '@/helpers/cn';
import { findValidImageUrl } from '@/helpers/utils';
import type { BlogGameResult } from '@/server/routers/blog';

const props = defineProps<{
  game: BlogGameResult;
}>();

const tierItemsToShow = computed(() =>
  [
    props.game?.tierItems?.find(ti => ti.tierLane?.tierSetSlug === 'hypeness')!,
    props.game?.tierItems?.find(ti => ti.tierLane?.tierSetSlug === 'suckz' && ti.reviewTitle)! ||
      props.game?.tierItems?.find(ti => ti.tierLane?.tierSetSlug === 'suckz')!,
  ].filter(ti => !!ti),
);

const reviewTitle = computed(() => {
  return tierItemsToShow.value[1]?.reviewTitle ?? '';
});

const finalImageUrl = computed(() => {
  return findValidImageUrl(props.game);
});

const createdAtFormatted = computed(() => {
  return format(tierItemsToShow.value[1].createdAt, 'dd/MM/yy');
});
</script>

<template>
  <div
    :class="
      cn(
        'relative block h-full w-full rounded-lg shadow-sm transition-all',
        'overflow-hidden whitespace-nowrap',
        'duration-200 ease-in-out hover:shadow-md',
        'dark:text-gray-200',
        'border border-gray-200/80 hover:border-gray-300 dark:border-gray-700/50 dark:hover:border-gray-600',
      )
    "
  >
    <div
      class="flex flex-row items-center gap-2 bg-size-[30%] bg-left bg-no-repeat px-1 py-1"
      :style="{
        backgroundImage: `linear-gradient(to right, rgba(0,0,0,0) 0%, var(--color-theme-bg) 90%), url(${finalImageUrl})`,
      }"
    >
      <!-- Main Title: Game Name + Review Title -->
      <div class="font-bold text-gray-900 text-shadow-lg md:ml-24 dark:text-white">
        <span>{{ game.gameName ?? '' }}</span>
      </div>
      <TierItemLinks :tierItem="{ ...tierItemsToShow[1], gameSteam: game }" />
      <div class="absolute right-1 flex items-center gap-1">
        <TagLink
          v-for="(review, i) in tierItemsToShow"
          :key="review.id"
          :tierSetSlug="review.tierLane!.tierSetSlug"
          :tierLane="review.tierLane!"
          :class="cn(i === 0 ? 'md:w-16' : 'md:w-22', 'h-3 w-4 md:h-full')"
        />
        <div class="font-mono text-xs">
          {{ createdAtFormatted }}
        </div>
      </div>
    </div>
  </div>
</template>
