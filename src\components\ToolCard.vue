<script setup lang="ts">
import type { TOOLS } from '@/helpers/tools';
import { pinia, useUserStore } from '@/stores/user';

import BaseContentCard from './BaseContentCard.vue';

const props = defineProps({
  tool: {
    type: Object as () => (typeof TOOLS)[number],
    required: true,
  },
});
const emit = defineEmits();
const userStore = useUserStore(pinia);
const { user } = storeToRefs(userStore);

const targetUrl = computed(() => {
  const url = `/tools/${props.tool.slug}/`;
  return url;
});

const backgroundUrl = computed(() => {
  const url = `/og-image/tools/${props.tool.slug}.png`;
  return url;
});

const authPassed = computed(() => !props.tool.authed || !!user.value);
</script>

<template>
  <BaseContentCard
    :url="targetUrl"
    :bg="backgroundUrl"
    :class="{
      'pointer-events-none brightness-50 contrast-75': tool.soon,
    }"
  >
    <template #title>
      {{ props.tool?.label ?? '' }}
    </template>
    <template #body>
      <span>
        {{ props.tool.desc }}
      </span>
      <div v-if="tool.soon" class="text-sm font-thin italic">coming soon...</div>
      <div v-if="!authPassed" class="text-sm font-thin text-red-400">please login first</div>
    </template>
  </BaseContentCard>
</template>
