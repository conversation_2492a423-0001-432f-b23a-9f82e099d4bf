import { z } from 'zod';

import { TRPCError } from '@trpc/server';

import { kysely } from '@/database/kysely';

import { createRouter, publicProcedure } from '../trpc';
import { filterSchema } from './gameSteam';

export const filterPresetRouter = createRouter({
  list: publicProcedure.query(async ({ ctx: { user } }) => {
    const query = kysely
      .selectFrom('filterPreset')
      .select(['id', 'name', 'filters', 'sorts', 'createdAt', 'public', 'userId'])
      .where(eb =>
        eb.or([
          // Show all public presets
          eb('public', '=', true),
          // Show user's private presets if logged in
          user ? eb.and([eb('userId', '=', user.id), eb('public', '=', false)]) : eb('id', '=', -1), // Never true if not logged in
        ]),
      )
      .orderBy('createdAt', 'desc');

    const results = (await query.execute()).map(row => ({
      ...row,
      filters: row.filters as typeof filterSchema.shape.filters._type,
      sorts: row.sorts as typeof filterSchema.shape.sorts._type,
      isOwner: user?.id === row.userId,
    }));

    return results;
  }),

  togglePublic: publicProcedure
    .input(
      z.object({
        id: z.number(),
        public: z.boolean(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) throw new TRPCError({ code: 'UNAUTHORIZED' });

      const result = await kysely
        .updateTable('filterPreset')
        .set({ public: input.public })
        .where('id', '=', input.id)
        .where('userId', '=', user.id)
        .returningAll()
        .executeTakeFirst();

      if (!result) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Filter preset not found or access denied',
        });
      }

      return result;
    }),

  create: publicProcedure
    .input(
      z.object({
        name: z.string(),
        filters: filterSchema.shape.filters,
        sorts: filterSchema.shape.sorts,
        public: z.boolean().default(false),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) throw new TRPCError({ code: 'UNAUTHORIZED' });

      const result = await kysely
        .insertInto('filterPreset')
        .values({
          userId: user.id,
          name: input.name,
          filters: JSON.stringify(input.filters),
          sorts: JSON.stringify(input.sorts),
          public: input.public,
        })
        .returningAll()
        .executeTakeFirst();

      return result;
    }),

  update: publicProcedure
    .input(
      z.object({
        id: z.number(),
        name: z.string(),
        filters: filterSchema.shape.filters,
        sorts: filterSchema.shape.sorts,
        public: z.boolean(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) throw new TRPCError({ code: 'UNAUTHORIZED' });

      const result = await kysely
        .updateTable('filterPreset')
        .set({
          name: input.name,
          filters: JSON.stringify(input.filters),
          sorts: JSON.stringify(input.sorts),
          public: input.public,
        })
        .where('id', '=', input.id)
        .where('userId', '=', user.id)
        .returningAll()
        .executeTakeFirst();

      if (!result) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Filter preset not found or access denied',
        });
      }

      return result;
    }),

  delete: publicProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) throw new TRPCError({ code: 'UNAUTHORIZED' });

      await kysely
        .deleteFrom('filterPreset')
        .where('id', '=', input.id)
        .where('userId', '=', user.id)
        .execute();
    }),
});
