---
import { getSupabaseServerClient } from '@/database/supabase.server';
import Layout from '@/layouts/Layout.astro';
import { type APIRoute } from 'astro';

export const GET: APIRoute = async ({ request, cookies, redirect }) => {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') || '/';

  if (code) {
    const supabase = getSupabaseServerClient(request.headers.get('Cookie'), cookies);

    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      return redirect(next);
    }
  }

  // return the user to an error page with instructions
  return redirect('/auth/auth-code-error');
};
---

<Layout title="Please wait..." showAuth showNavbar={false}>
  <main>
    <div
      class="absolute inset-0 flex min-h-screen flex-col items-center justify-center px-4 sm:px-6"
    >
      <span class="text-9xl"> Please wait...</span>
      <a
        href="/"
        type="button"
        class="mt-8 inline-flex items-center rounded-lg border border-gray-700 bg-gray-800 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-hidden focus:ring-4 focus:ring-gray-600"
      >
        <span class="text-3xl">Back</span>
      </a>
    </div>
  </main>
</Layout>

<script>
  import { supabase } from '@/database/supabase.browser';

  supabase.auth.onAuthStateChange((event, session) => {
    window.location.href = '/tools';
  });
</script>
