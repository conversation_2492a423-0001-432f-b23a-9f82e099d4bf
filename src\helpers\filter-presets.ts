import { type DataTableOperatorFilterMetaData } from 'primevue/datatable';

import { FilterMatchMode, FilterOperator } from '@primevue/core/api';

type FilterPreset = { label: string; value: { [key: string]: DataTableOperatorFilterMetaData } };

export const tagPresets: FilterPreset[] = [
  {
    label: 'Co-op',
    value: {
      tags: {
        operator: FilterOperator.AND,
        constraints: [
          {
            value: ['Online PvP', 'Online Co-op', 'Online Co-Op'],
            matchMode: FilterMatchMode.CONTAINS,
          },
        ],
      },
    },
  },
  {
    label: 'Co-op Horror',
    value: {
      tags: {
        operator: FilterOperator.AND,
        constraints: [
          { value: ['Online Co-op', 'Online PvP', 'Online Co-Op'], matchMode: 'contains' },
          { value: ['Horror', 'Psychological Horror', 'Survival Horror'], matchMode: 'contains' },
        ],
      },
    },
  },
];

export const ratingPresets: FilterPreset[] = [
  {
    label: 'To Rate',
    value: {
      hypeness: {
        operator: FilterOperator.AND,
        constraints: [
          {
            value: [''],
            matchMode: FilterMatchMode.CONTAINS,
          },
        ],
      },
    },
  },
  {
    label: 'To Play',
    value: {
      hypeness: {
        operator: FilterOperator.AND,
        constraints: [
          {
            value: ['hypeness-bias', 'hypeness-hype', 'hypeness-cool', 'hypeness-sus'],
            matchMode: FilterMatchMode.CONTAINS,
          },
        ],
      },
      suckz: {
        operator: FilterOperator.AND,
        constraints: [
          {
            value: [''],
            matchMode: FilterMatchMode.CONTAINS,
          },
        ],
      },
    },
  },
  {
    label: 'Played',
    value: {
      suckz: {
        operator: FilterOperator.AND,
        constraints: [
          {
            value: ['suckz-s', 'suckz-u', 'suckz-c', 'suckz-k'],
            matchMode: FilterMatchMode.CONTAINS,
          },
        ],
      },
    },
  },
];
