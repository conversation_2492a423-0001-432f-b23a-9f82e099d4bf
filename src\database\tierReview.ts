import { format } from 'date-fns';
import { th } from 'date-fns/locale';

export type ComputedPropertiesOfTierReview = {
  publishDate?: Date;
  publishDateFormatted?: string;
};

export function applyComputedTierReview<T extends object>(input: T) {
  const applied: Omit<T, 'publishDate'> & ComputedPropertiesOfTierReview = input as any;

  if ('publishDate' in applied) {
    if (typeof applied.publishDate === 'string') {
      applied.publishDate = new Date(applied.publishDate);
    }
  }

  applied.publishDateFormatted = (() => {
    return applied.publishDate ? format(applied.publishDate, 'dd MMMM yyyy', { locale: th }) : '';
  })();

  return applied;
}
