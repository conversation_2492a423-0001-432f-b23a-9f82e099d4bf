/// <reference types="vitest" />
import { resolve } from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [
    AutoImport({
      imports: ['vue', 'pinia'],
      dts: false,
    }),
  ],
  test: {
    alias: [
      { find: '@', replacement: resolve(__dirname, './src') },
      { find: '_tests', replacement: resolve(__dirname, './tests') },
    ],
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    testTimeout: 60_000,
  },
});
