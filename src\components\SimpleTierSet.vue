<script setup lang="ts">
import * as _ from 'lodash-es';
import ConfirmPopup from 'primevue/confirmpopup';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import type { SortableOptions } from 'sortablejs';
import Draggable from 'vuedraggable';

import { Icon } from '@iconify/vue';

import { cn } from '@/helpers/cn';
import type { SimpleTierLaneObject, SimpleTierSetInstance } from '@/server/routers/simpleTier';
import { trpc } from '@/services/trpc';
import { useUserStore } from '@/stores/user';

import SimpleTierLane from './SimpleTierLane.vue';

const getDefaultInstance = (): SimpleTierSetInstance => ({
  spaceId: null,
  id: null,
  label: 'My New Tier',
  lexorank: '',
  jsonObject: {
    items: [
      {
        id: ++runningLaneId,
        label: 'S',
        mainColor: '#efbf04',
        textColor: '#fff',
        items: [],
      },
      {
        id: ++runningLaneId,
        label: 'A',
        mainColor: '#0e0',
        textColor: '#fff',
        items: [],
      },
      {
        id: ++runningLaneId,
        label: 'B',
        mainColor: '#ef0',
        textColor: '#aaa',
        items: [],
      },
      {
        id: ++runningLaneId,
        label: 'C',
        mainColor: '#e50',
        textColor: '#fff',
        items: [],
      },
      {
        id: ++runningLaneId,
        label: 'D',
        mainColor: '#f00',
        textColor: '#fff',
        items: [],
      },
      {
        id: ++runningLaneId,
        label: 'None',
        mainColor: '#000',
        textColor: '#fff',
        items: [],
      },
    ],
  },
  canEdit: true,
});

const props = defineProps({
  instance: {
    type: Object as () => SimpleTierSetInstance,
    required: false,
  },
});

const emit = defineEmits<{
  (e: 'delete'): void;
  (e: 'save'): void;
}>();

const toast = useToast();
const { space } = storeToRefs(useUserStore());

let runningLaneId = new Date().valueOf();

const originalInstance = reactive(getDefaultInstance());
const instance = reactive(getDefaultInstance());

const isLoading = ref(false);
const canEdit = ref(true);
const canSave = computed(() => space.value?.id);

function loadInstance(newInstance: SimpleTierSetInstance) {
  Object.assign(originalInstance, _.cloneDeep(newInstance));
  Object.assign(instance, _.cloneDeep(newInstance));
  canEdit.value = newInstance?.canEdit ?? false;
}

async function saveInstance({ forceNew = false } = {}) {
  isLoading.value = true;
  try {
    const toUpdate = { ...instance } as typeof instance & { spaceId: number };

    if (forceNew) {
      toUpdate.id = null;
    }

    if (!toUpdate.spaceId && space.value) {
      toUpdate.spaceId = space.value.id;
    }

    const updated = await trpc.simpleTier.upsertMany.mutate({
      items: [toUpdate],
    });
    if (!updated) {
      throw new Error('Failed to update tier');
    }
    console.log('Updated simpleTier:', instance.label);

    toast.add({
      severity: 'success',
      summary: 'Updated',
      detail: instance.label,
      life: 5000,
    });

    emit('save');
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't update Tierset",
      detail: 'Maybe the something is already taken?, Or just the internet mystery',
      life: 20000,
    });
  }

  isLoading.value = false;
}

async function addLane() {
  isLoading.value = true;
  instance.jsonObject.items.push({
    id: ++runningLaneId,
    label: 'New Lane',
    mainColor: '#000000',
    textColor: '#fff',
    items: [],
  });
  isLoading.value = false;
}

async function deleteLane(index: number) {
  isLoading.value = true;
  instance.jsonObject.items = [
    ...instance.jsonObject.items.slice(0, index),
    ...instance.jsonObject.items.slice(index + 1),
  ];
  isLoading.value = false;
}

const confirm = useConfirm();
async function deleteTierset(evt: Event) {
  confirm.require({
    group: 'delete-tierset',
    target: evt.currentTarget as HTMLElement,
    accept: async () => {
      isLoading.value = true;
      if (instance.id) {
        await trpc.simpleTier.delete.mutate({
          id: instance.id,
        });
      }
      loadInstance(getDefaultInstance());
      isLoading.value = false;
      emit('delete');
    },
  });
}

function exportInstance() {
  const toExport = _.pick(instance, ['label', 'jsonObject']);
  const blob = new Blob([JSON.stringify(toExport)], {
    type: 'application/json',
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${_.kebabCase(toExport.label)}-demoman.json`;
  a.click();
  URL.revokeObjectURL(url);
  console.log('Exported:', toExport);
}

function importInstance() {
  const el = document.getElementById('file-import') as HTMLInputElement;
  el.value = '';
  el.click();
}

function onImportFileChange() {
  const el = document.getElementById('file-import') as HTMLInputElement;
  const file = el.files![0];
  const reader = new FileReader();
  reader.onload = () => {
    try {
      const result = JSON.parse(reader.result as string);
      loadInstance(result);
      el.value = '';
      console.log('Imported:', instance);
    } catch {
      toast.add({
        severity: 'error',
        summary: "Can't import file",
        detail: 'Something is wrong with the file',
        life: 20000,
      });
    }
  };
  reader.readAsText(file);
}

const options = computed<SortableOptions>(() => {
  return {
    group: { name: 'sets' },
    draggable: '.draggable',
    handle: '.handle',
    filter: '.ignore-drag',
    preventOnFilter: false,
    delay: 300,
    delayOnTouchOnly: true,
    animation: 150,
    ghostClass: 'ghost',
    dragClass: 'drag',
    forceFallback: true,
    scroll: true,
    bubbleScroll: true,
    disabled: !canEdit.value,
  };
});

watch(
  () => props.instance,
  val => {
    if (val) {
      console.log('Instance Loaded:', val);
      loadInstance(val);
    } else {
      console.log('Instance reset');
      loadInstance(getDefaultInstance());
    }
  },
  { immediate: true },
);
</script>

<template>
  <div :class="cn('mb-2 w-full')">
    <div :class="cn('flex w-full items-center justify-start gap-1 p-1 text-lg!')">
      <div :class="cn('flex w-full items-center')">
        <Icon icon="lucide:text" />
        <PrimeInputText
          v-model="instance.label"
          inputId="tier-set-label"
          :placeholder="instance.label || 'No name'"
          :class="[
            'border-none bg-transparent!',
            'placeholder:text-(--main)! placeholder:opacity-30',
            '-ml-6! px-0! py-0! indent-8 font-black',
          ]"
        />
      </div>
      <ConfirmPopup group="delete-tierset">
        <template #container="{ acceptCallback, rejectCallback }">
          <div :class="cn('rounded p-4')">
            <div>
              Are you sure you want to delete
              <strong :class="cn('text-red-400')">WHOLE SET "{{ instance.label }}"</strong>
              ?
            </div>
            <div :class="cn('mt-4 flex items-center gap-2 text-xs')">
              <PrimeButton
                :class="cn('bg-red-600! hover:bg-red-500!')"
                label="DELETE!"
                @click="acceptCallback"
              />

              <PrimeButton
                :class="cn('bg-gray-500! hover:bg-gray-500/50!')"
                label="No"
                @click="rejectCallback"
              />
            </div>
          </div>
        </template>
      </ConfirmPopup>
    </div>
  </div>
  <Draggable
    v-model="instance.jsonObject.items"
    item-key="id"
    tag="div"
    v-bind="options"
    :class="cn('flex flex-col items-center gap-0.5')"
  >
    <template #item="{ element: lane, index: i }: { element: SimpleTierLaneObject; index: number }">
      <SimpleTierLane
        v-model:tierLane="instance.jsonObject.items[i]"
        :key="lane.id"
        :canAdd="i === instance.jsonObject.items.length - 1"
        :class="cn('draggable')"
        @delete:tierLane="deleteLane(i)"
      />
    </template>
    <template #footer> </template>
  </Draggable>
  <div :class="cn('my-2 flex gap-8')">
    <div :class="cn('flex gap-1')">
      <PrimeButton :disabled="isLoading" @click="addLane">
        <Icon icon="lucide:plus" />
        <span>Add Lane</span>
      </PrimeButton>
    </div>

    <div :class="cn('flex gap-1')">
      <template v-if="instance.id && canEdit">
        <PrimeButton
          v-if="canSave"
          :disabled="isLoading"
          :class="cn('bg-green-600! hover:bg-green-500!')"
          @click="saveInstance"
        >
          <Icon icon="lucide:save" />
          <span>Save Update</span>
        </PrimeButton>
        <PrimeButton
          v-if="canSave"
          :disabled="isLoading"
          :class="cn('bg-cyan-500! hover:bg-cyan-400!')"
          @click="saveInstance({ forceNew: true })"
        >
          <Icon icon="lucide:save-all" />
          <span>Save As New</span>
        </PrimeButton>
      </template>
      <template v-else>
        <PrimeButton
          v-if="canSave"
          :disabled="isLoading"
          :class="cn('bg-cyan-500! hover:bg-cyan-400!')"
          @click="saveInstance({ forceNew: true })"
        >
          <Icon icon="lucide:save-all" />
          <span>Save As New</span>
        </PrimeButton>
      </template>
    </div>

    <div :class="cn('flex gap-1')">
      <PrimeButton
        :disabled="isLoading"
        :class="cn('bg-yellow-600! hover:bg-yellow-500!')"
        @click="exportInstance"
      >
        <Icon icon="lucide:download" />
        <span>Export</span>
      </PrimeButton>
      <PrimeButton
        :disabled="isLoading"
        :class="cn('bg-yellow-600! hover:bg-yellow-500!')"
        @click="importInstance"
      >
        <Icon icon="lucide:upload" />
        <span>Import</span>
        <input
          type="file"
          id="file-import"
          class="invisible absolute"
          @change="onImportFileChange"
        />
      </PrimeButton>
    </div>

    <PrimeButton
      :class="
        cn(
          'bg-transparent! text-xs! text-red-500! outline-1! outline-red-600!',
          'hover:animate-wiggle-more hover:bg-red-200! hover:animate-duration-75 hover:animate-infinite',
        )
      "
      @click="deleteTierset"
      :loading="isLoading"
    >
      <Icon icon="lucide:bomb" />
    </PrimeButton>
  </div>
</template>
